import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default function init(plop) {
  plop.setGenerator('rsource-api', {
    description: 'This will generate a resource api under a module',
    prompts: [
      {
        type: 'input',
        name: 'modulePath',
        message: 'Enter the module path.(relative to apps/api/src/modules)',
      },
      {
        type: 'input',
        name: 'resourceName',
        message: 'Enter the resource name.(e.g. Product Variation)',
      },
    ],
    actions: function (data) {
      const pascalCase = plop.getHelper('pascalCase');
      const dashCase = plop.getHelper('dashCase');

      const controllerClassName = pascalCase(data.resourceName) + 'ResourceController';
      const controllerFileName = dashCase(data.resourceName) + '-resource.controller.ts';
      return [
        {
          type: 'add',
          path: `apps/api/src/modules/{{modulePath}}/controllers/${controllerFileName}`,
          templateFile: path.join(__dirname, 'templates/controller.template.hbs'),
        },
        {
          type: 'modify',
          path: 'apps/api/src/modules/{{modulePath}}/{{dashCase modulePath}}.module.ts',
          pattern: /(controllers:\s*\[)([^]*)\]/,
          template: `$1$2, ${controllerClassName}]`,
        },
      ];
    },
  });
}
