import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { {{properCase resourceName}} } from '../entities/{{dashCase resourceName}}.entity';
import { Like, Repository } from 'typeorm';
import { ResourceModel } from '@common-utils/resource-type';

@ApiTags('Resource')
@Controller('/resource/{{dashCase resourceName}}')
export class {{properCase resourceName}}ResourceController {
  constructor(
    @InjectRepository({{properCase resourceName}})
    private readonly repository: Repository<{{properCase resourceName}}>,
  ) {}
  @Get()
  async list(@Query('search') search: string): Promise<ResourceModel[]> {
    const resources = await this.repository.find({
      select: { name: true, id: true },
      where: [{ name: Like(`%${search}%`) }],
      take: 15
    });
    return resources.map((resource) => ({ identifier: resource.id, displayText: resource.name }));
  }
}
