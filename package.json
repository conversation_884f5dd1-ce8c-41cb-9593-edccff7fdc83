{"name": "dyspxn-erp", "version": "1.0.0", "private": "true", "main": "index.js", "scripts": {"prepare": "husky", "format": "prettier --write --ignore-unknown .", "plop": "plop"}, "keywords": [], "author": "", "license": "ISC", "description": "", "packageManager": "yarn@1.22.22", "engines": {"node": ">=22.13.0"}, "workspaces": ["apps/*", "libs/*"], "devDependencies": {"husky": "^9.1.7", "lint-staged": "^15.5.1", "plop": "^4.0.1", "prettier": "^3.4.2"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}}