import { readdirSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const baseDir = path.join(__dirname);

const entries = readdirSync(baseDir, { withFileTypes: true, recursive: true })
  .filter((entry) => entry.isFile() && entry.name.endsWith('.plop.mjs'))
  .map((entry) => path.join(entry.parentPath, entry.name));

export default async function (plop) {
  for (const entry of entries) {
    const module = await import(entry);
    if (module.default) {
      module.default(plop);
    }
  }
}
