export type FormInputType =
  | 'TEXT_INPUT'
  | 'TEXT_AREA'
  | 'SINGLE_SELECT_BOX'
  | 'MULTI_SELECT_BOX'
  | 'SINGLE_RESOURCE_SELECTOR'
  | 'MULTI_RESOURCE_SELECTOR'
  | 'WEIGHT_SELECTOR'
  | 'DIMENSION_SELECTOR';

export type SelectOption = {
  value: string;
  label: string;
};

/**
 * How the field will be displayed on Form
 */
export type FormLayout = {
  inputType: FormInputType;
  displayOrder: number;
  width: 'full' | 'half';
};

export type ProductTypeAttribute = {
  slug: string;
  displayName: string;
  displayOrder: string;
  dataType: string;
  allowMultiple: boolean;
  isSearchable: boolean;
  isFacet: boolean;
  isSEOAttrib: boolean;
  isRequired: boolean;
  formLayout: FormLayout;
  metadata: {
    options?: SelectOption[];
  };
};
