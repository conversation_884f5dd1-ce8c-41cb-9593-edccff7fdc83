GET /catalogs
  Get catalogs List
GET /catalogs/{catalogId}
  Get catalog by id
POST /catalogs
  Create catalog
PUT /catalogs/{catalogId}
  Update catalog
DELETE /catalogs/{catalogId}
  Delete catalog
------------------------------------
GET /categories
  Get categories List
GET /categories/{categoryId}
  Get category by id
POST /categories
  Create category
PUT /categories/{categoryId}
  Update category
DELETE /categories/{categoryId}
  Delete category
------------------------------------
GET /product-types
  Get products List  
GET /product-types/{productTypeId}
  Get product by id
POST /product-types
  Create product
  {
    name: string,
    description: string,
    attributes: [
      {
        product_type_id: number,
        name: string,
        data_type: string,
        allow_multiple: boolean,
        is_variant_attribute: boolean,
        is_searchable: boolean,
        is_facet: boolean,
        is_seo_attrib: boolean,
        is_required: boolean,
        meta_data: json,
      }
    ]
  }
PUT /product-types/{productTypeId}
  Update product
  {
    name: string,
    description: string,
    attributes: [
      {
        product_type_id: number,
        name: string,
        data_type: string,
        allow_multiple: boolean,
        is_variant_attribute: boolean,
        is_searchable: boolean,
        is_facet: boolean,
        is_seo_attrib: boolean,
        is_required: boolean,
        meta_data: json,
      }
    ]
  }
DELETE /product-types/{productTypeId}
  Delete product
-------------------------------------
GET /publishers
  Get publishers List
GET /publishers/{publisherId}
  Get publisher by id
POST /publishers
  Create publisher
PUT /publishers/{publisherId}
  Update publisher
DELETE /publishers/{publisherId}
  Delete publisher
  Get products List
-------------------------------------
GET /people
  Get people List
GET /people/{personId}
  Get person by id
POST /people
  Create person
PUT /people/{personId}
  Update person
DELETE /people/{personId}
  Delete person
  Get products List