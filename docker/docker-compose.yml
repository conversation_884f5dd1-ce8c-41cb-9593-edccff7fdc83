services:
  phpmyadmin:
    image: phpmyadmin:5.2.2
    links:
      - mysql_8_4_5
    environment:
      PMA_HOST: mysql_8_4_5
      PMA_PORT: 3306
      UPLOAD_LIMIT: **********
    ports:
      - '4500:80'
  mysql_8_4_5:
    image: mysql:8.4.5
    restart: always
    ports:
      - '8306:3306'
      - '8060:33060'
      - '8061:33061'
    environment:
      MYSQL_RANDOM_ROOT_PASSWORD: 1
      MYSQL_USER: 'dyspxn-erp-db-user'
      MYSQL_PASSWORD: 'password'
      MYSQL_DATABASE: 'dyspxn-erp-db'
    volumes:
      - 'dyspxn-erp-mysql-data:/var/lib/mysql'
    healthcheck:
      test:
        - CMD
        - mysqladmin
        - ping
        - '-pPassword'
      retries: 3
      timeout: 5s
  typesense:
    image: typesense/typesense:28.0
    restart: on-failure
    ports:
      - '8108:8108'
    volumes:
      - dyspxn-erp-typesense-data:/data
    command: '--data-dir /data --api-key=hUxKG35zDq3B2CAYYJkJN5LDExvsanVt --enable-cors'
volumes:
  dyspxn-erp-mysql-data:
  dyspxn-erp-typesense-data:
