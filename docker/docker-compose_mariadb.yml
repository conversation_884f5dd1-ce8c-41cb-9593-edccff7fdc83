services:
  phpmyadmin:
    image: phpmyadmin:5.2.2
    links:
      - mariadb_11_4
    environment:
      PMA_HOST: mariadb_11_4
      PMA_PORT: 3306
      UPLOAD_LIMIT: **********
    ports:
      - '4500:80'
  mariadb_11_4:
    image: mariadb:11.3.2
    restart: always
    ports:
      - '8306:3306'
      - '8060:33060'
      - '8061:33061'
    environment:
      MARIADB_RANDOM_ROOT_PASSWORD: 1
      MARIADB_USER: 'dyspxn-erp-db-user'
      MARIADB_PASSWORD: 'password'
      MARIADB_DATABASE: 'dyspxn-erp-db'
    volumes:
      - 'dyspxn-erp-mariadb-data:/var/lib/mysql'
    healthcheck:
      test:
        - CMD
        - mysqladmin
        - ping
        - '-pPassword'
      retries: 3
      timeout: 5s
  typesense:
    image: typesense/typesense:28.0
    restart: on-failure
    ports:
      - '8108:8108'
    volumes:
      - dyspxn-erp-typesense-data:/data
    command: '--data-dir /data --api-key=hUxKG35zDq3B2CAYYJkJN5LDExvsanVt --enable-cors'
volumes:
  dyspxn-erp-mariadb-data:
  dyspxn-erp-typesense-data:
