services:
  postgres_17_5:
    image: postgres:17.5-bullseye
    restart: unless-stopped
    environment:
      POSTGRES_USER: dyspxn-erp-db-user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dyspxn-erp-db
    ports:
      - '7432:5432'
    volumes:
      - dyspxn-erp-pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s

  pgadmin4_9_4:
    image: dpage/pgadmin4:9.4
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - '8123:80'
    depends_on:
      postgres_17_5:
        condition: service_healthy
    volumes:
      - dyspxn-erp-pgadmin-data:/var/lib/pgadmin
      - ./pgadmin-server.json:/pgadmin4/servers.json

  typesense_28:
    image: typesense/typesense:28.0
    restart: on-failure
    ports:
      - '9108:8108'
    volumes:
      - dyspxn-erp-typesense-data:/data
    command: '--data-dir /data --api-key=hUxKG35zDq3B2CAYYJkJN5LDExvsanVt --enable-cors'
volumes:
  dyspxn-erp-pgdata:
  dyspxn-erp-pgadmin-data:
  dyspxn-erp-typesense-data:
