import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../auth.service';
import { User } from '@api/modules/usermgmt/entities/user.entity';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super();
  }

  async validate(username: string, password: string): Promise<User> {
    const user: User | null = await this.authService.validateUser({
      username,
      password,
    });
    if (!user) {
      throw new UnauthorizedException('Invalid username or password');
    }
    return user;
  }
}
