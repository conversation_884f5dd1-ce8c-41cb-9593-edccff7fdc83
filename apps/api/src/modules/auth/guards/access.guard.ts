import { UsermgmtService } from '@api/modules/usermgmt/usermgmt.service';
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtPayloadDto } from '../dto/jwt-payload.dto';

@Injectable()
export class AccessGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly userService: UsermgmtService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>('permissions', context.getHandler());

    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest<{ user?: JwtPayloadDto }>();
    const user = request.user;

    if (!user || !user.id || !user.activeOpUnite) {
      return false;
    }
    const userData = await this.userService.getUserById(Number(user.id));
    if (!userData) {
      return false;
    }
    // Check the Active Operation Unit
    if (user?.activeOpUnite && user?.activeOpUnite === 'APPLICATION') {
      if (userData?.role && userData?.role && userData?.role?.name === 'Administrator') {
        return true;
      } else {
        const platformAccess = await this.userService.checkAccessToResource(Number(user.id), requiredPermissions);
        if (platformAccess) {
          return true;
        } else {
          return false;
        }
      }
    } else {
      const platformAccess = await this.userService.checkPermissionToOpunitResource(
        Number(user.id),
        Number(user.activeOpUnite),
        requiredPermissions,
      );
      if (platformAccess) {
        return true;
      } else {
        return false;
      }
    }
  }
}
