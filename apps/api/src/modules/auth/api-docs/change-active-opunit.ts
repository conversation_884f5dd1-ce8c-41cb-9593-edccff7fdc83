import { ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ChangeActiveOpunitSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Change active opunit' }),
    ApiBody({
      schema: {
        type: 'object',
        required: ['opunitId'],
        properties: {
          opunitId: {
            type: 'string',
            example: '1',
            description: 'The ID of the opunit to be set as active.',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Active opunit changed successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Active opunit changed successfully',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          message: 'Unauthorized',
          statusCode: 401,
        },
      },
    }),
  );
}
