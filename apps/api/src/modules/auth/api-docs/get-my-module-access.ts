import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function GetMyModuleAccess() {
  return applyDecorators(
    ApiOperation({
      summary: 'Get my module access',
      description: 'Get my module access',
    }),
    ApiResponse({
      status: 200,
      description: 'Get my module access',
      schema: {
        example: {
          status: 'success',
          message: 'Get my module access',
          data: {
            modules: ['<string>', '<string>'],
            groups: ['<string>', '<string>'],
          },
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the opunit',
          data: [],
        },
      },
    }),
  );
}
