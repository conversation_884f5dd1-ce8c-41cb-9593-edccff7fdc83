import { ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function GetMyOpunitSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get my opunit' }),
    ApiResponse({
      status: 200,
      description: 'User opunit retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'User opunit retrieved successfully',
          data: {
            label: '<string>',
            value: '<string>',
          },
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          message: 'Unauthorized',
          statusCode: 401,
        },
      },
    }),
  );
}
