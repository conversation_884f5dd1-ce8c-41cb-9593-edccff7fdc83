import { ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function LoginSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'User login' }),
    ApiBody({
      description: 'User login payload',
      schema: {
        type: 'object',
        required: ['username', 'password'],
        properties: {
          username: {
            type: 'string',
            example: '<EMAIL>',
            description: 'User email address',
          },
          password: {
            type: 'string',
            example: 'password',
            description: 'User password',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'User logged in successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'User logged in successfully',
          data: {
            user: {
              id: '<number>',
              name: '<string>',
              emailId: '<string>',
              password: '<hashed_password>',
              mobileNo: '<string>',
              role: {
                id: '<number>',
                name: '<string>',
                description: '<string>',
                isReadOnly: '<boolean>',
                rolePosition: '<number>',
              },
              isFirstLogin: '<boolean>',
              passwordRetryLeft: '<number>',
            },
            activeOpUnite: '<string>',
            token: '<jwt_token>',
          },
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Invalid access.',
      schema: {
        example: {
          message: 'Invalid username or password',
          error: 'Unauthorized',
          statusCode: 401,
        },
      },
    }),
  );
}
