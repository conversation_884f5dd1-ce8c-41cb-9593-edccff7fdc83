import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ProfileSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get user profile' }),
    ApiResponse({
      status: 200,
      description: 'User profile retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: {
            user: {
              id: '<number>',
              name: '<string>',
              emailId: '<string>',
              password: '<hashed_password>',
              mobileNo: '<string>',
              role: {
                id: '<number>',
                name: '<string>',
                description: '<string>',
                isReadOnly: '<boolean>',
                rolePosition: '<number>',
              },
              isFirstLogin: '<boolean>',
              passwordRetryLeft: '<number>',
            },
          },
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          message: 'Unauthorized',
          statusCode: 401,
        },
      },
    }),
  );
}
