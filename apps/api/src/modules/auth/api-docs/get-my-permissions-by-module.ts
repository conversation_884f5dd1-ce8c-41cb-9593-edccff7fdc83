import { ApiParam, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function GetMyPermissionsByModule() {
  return applyDecorators(
    ApiOperation({
      summary: 'Get my permissions by module',
      description: 'Get my permissions by module',
    }),
    ApiParam({
      name: 'module',
      description: 'Module name',
      required: true,
      type: String,
    }),
    ApiResponse({
      status: 200,
      description: 'Get my permissions by module',
      schema: {
        example: {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: {
            modules: ['<string>', '<string>'],
            groups: ['<string>', '<string>'],
          },
        },
      },
    }),
    // This is a bit confusing, as the same status code is used for different errors
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'No permissions assigned to this role',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the opunit',
          data: [],
        },
      },
    }),
  );
}
