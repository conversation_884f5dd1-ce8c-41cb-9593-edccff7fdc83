import { Injectable } from '@nestjs/common';
import { UsermgmtService } from '@/modules/usermgmt/usermgmt.service';
import { JwtService } from '@nestjs/jwt';
import { User } from '@/modules/usermgmt/entities/user.entity';
import { AuthPayloadDto } from './dto/auth-payload.dto';
import * as bcrypt from 'bcrypt';
import { JwtPayloadDto } from './dto/jwt-payload.dto';
import { Opunit } from '../opunitmgmt/entities/operationalunit.entity';
import { RoleService } from '../rbac/services/role.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UsermgmtService,
    private readonly jwtService: JwtService,
    private readonly roleService: RoleService,
  ) {}

  //Validate User
  async validateUser(authPayload: AuthPayloadDto): Promise<User | null> {
    const { username, password } = authPayload;
    const user = await this.userService.getUser({
      where: { emailId: username },
    });
    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }
    return null;
  }

  //Generate JWT Token and save that token in the cookie
  generateJwtToken(payload: JwtPayloadDto): string {
    const token = this.jwtService.sign(payload);
    return token;
  }

  getUser(id: number): Promise<User | null> {
    return this.userService.getUserById(id);
  }

  async getAllOpunits(): Promise<Opunit[]> {
    return await this.userService.getAllOpunits();
  }

  async getUserOpunitAccess(userId: number): Promise<any[]> {
    return await this.userService.getUsersOpunitAccess(userId);
  }

  async getOpunitById(id: number): Promise<Opunit | null> {
    return await this.userService.validateOpunit(id);
  }

  async getOpunitAccess(userId: number, opunitId: number): Promise<boolean> {
    return await this.userService.getOpunitAccess(userId, opunitId);
  }

  async getAccessByRole(roleId: number): Promise<any> {
    return await this.roleService.getAccessByRole(roleId);
  }

  async getAccessByOpunit(userId: number, opunitId: number): Promise<any> {
    return await this.userService.getAccessByOpunit(userId, opunitId);
  }

  async getPermissionsByUserModule(userId: number, module: string): Promise<any> {
    return await this.userService.getPermissionsByUserModule(userId, module);
  }

  async getAccessByOpunitModule(userId: number, opunitId: number, module: string): Promise<any> {
    return await this.userService.getAccessByOpunitModule(userId, opunitId, module);
  }
}
