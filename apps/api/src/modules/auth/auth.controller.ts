import { Body, Controller, Get, Param, Post, Put, Req, Res, UnauthorizedException, UseGuards } from '@nestjs/common';
import { ApiBody, ApiCookieAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { AuthPayloadDto } from './dto/auth-payload.dto';
import { LocalGuard } from './guards/local.guard';
import { ApiResponseType } from 'src/types/api-response';
import { Response, Request } from 'express';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtPayloadDto } from './dto/jwt-payload.dto';
import { Permission } from '../rbac/entities/permission.entity';

@ApiTags('Authentication API')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseGuards(LocalGuard)
  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiBody({
    type: AuthPayloadDto,
    description: 'User login payload',
    examples: {
      example1: {
        value: {
          username: '<EMAIL>',
          password: 'password',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'User logged in successfully.' })
  @ApiResponse({ status: 401, description: 'Invalid access.' })
  async login(
    @Body() authPayload: AuthPayloadDto,
    @Res({ passthrough: true }) res: Response,
  ): Promise<ApiResponseType<any>> {
    const user = await this.authService.validateUser(authPayload);
    if (!user) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'Invalid access',
        data: [],
      });
    }
    let activeOpUnite = '';
    //Check if user has Administrator role
    if (user?.role) {
      activeOpUnite = 'APPLICATION';
    } else {
      // Get all operational units assigned to the user
      const opUnitsAccess = (await this.authService.getUserOpunitAccess(user.id)) as {
        id: number;
      }[];
      if (opUnitsAccess?.length > 0) {
        activeOpUnite = String(opUnitsAccess[0]?.id);
      }
    }
    if (!activeOpUnite) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'User Operational Unit not found',
        data: [],
      });
    }

    // Generate JWT token
    const token = this.authService.generateJwtToken({
      id: user.id,
      name: user.name,
      activeOpUnite: activeOpUnite,
      roleId: user?.role?.id ?? null,
    });

    // Set the token in a cookie
    res.cookie('auth_token', token, {
      httpOnly: true,
    });

    return {
      status: 'success',
      message: 'User logged in successfully',
      data: {
        user,
        activeOpUnite,
        token,
      },
    };
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Get('profile')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully.',
  })
  @ApiResponse({
    status: 401,
    description: 'User not found or invalid userId.',
  })
  async getProfile(@Req() req: Request) {
    const user = req.user as JwtPayloadDto;
    if (user?.id) {
      const userData = await this.authService.getUser(user.id);
      if (userData) {
        return {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: userData,
        };
      } else {
        throw new UnauthorizedException({
          status: 'error',
          message: 'User not found',
          data: [],
        });
      }
    } else {
      throw new UnauthorizedException({
        status: 'error',
        message: 'Invalid access',
        data: [],
      });
    }
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Post('logout')
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'User logged out successfully.' })
  logout(@Res({ passthrough: true }) res: Response) {
    res.clearCookie('auth_token');
    return {
      status: 'success',
      message: 'User logged out successfully',
      data: null,
    };
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Get('get-my-opunits')
  async getMyOpunits(@Req() req: Request) {
    const user = req.user as JwtPayloadDto;
    if (!user?.id) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'User not found',
        data: [],
      });
    }
    const userData = await this.authService.getUser(user.id);
    // If the user has the Administrator role, fetch all operational units
    if (userData?.role?.name === 'Administrator') {
      const allOpUnits = await this.authService.getAllOpunits();
      const opUnits = allOpUnits.map((opUnit: { id: number; name: string }) => ({
        label: opUnit.name,
        value: String(opUnit.id),
      }));
      opUnits.push({
        label: 'Main Application',
        value: 'APPLICATION',
      });
      return {
        status: 'success',
        message: 'Operational units retrieved successfully',
        data: opUnits,
      };
    }
    const opUnitsAccess = await this.authService.getUserOpunitAccess(user.id);
    const opUnits = opUnitsAccess?.length
      ? opUnitsAccess.map((opUnit: { id: number; name: string }) => ({
          label: opUnit.name,
          value: String(opUnit.id),
        }))
      : [];
    if (user?.roleId) {
      opUnits.push({
        label: 'Main Application',
        value: 'APPLICATION',
      });
    }
    return {
      status: 'success',
      message: 'User profile retrieved successfully',
      data: opUnits,
    };
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Put('change-active-opunit')
  @ApiBody({
    type: AuthPayloadDto,
    description: 'User change opunit payload',
    examples: {
      example1: {
        value: {
          opunitId: '1',
        },
      },
    },
  })
  async changeOpunit(
    @Req() req: Request,
    @Body() body: { opunitId: string },
    @Res({ passthrough: true }) res: Response,
  ) {
    const user = req.user as JwtPayloadDto;
    if (!user?.id) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'User not found',
        data: [],
      });
    }
    const userData = await this.authService.getUser(user.id);
    if (!userData) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'User not found',
        data: [],
      });
    }
    //Validate is the opunit is valid
    if (body.opunitId !== 'APPLICATION') {
      const opUnit = await this.authService.getOpunitById(Number(body.opunitId));
      if (!opUnit) {
        throw new UnauthorizedException({
          status: 'error',
          message: 'Opunit not found',
          data: [],
        });
      }
    }
    // Now Check the If User Has Administrator Role
    if (userData?.role?.name === 'Administrator') {
      // Update the active opunit to the JWT token
      const token = this.authService.generateJwtToken({
        id: user.id,
        name: user.name,
        activeOpUnite: String(body.opunitId),
        roleId: userData?.role?.id,
      });
      res.cookie('auth_token', token, {
        httpOnly: true,
      });
      return {
        status: 'success',
        message: 'Opunit changed successfully',
        data: null,
      };
    }
    // Now Validate if the user has access to the opunit
    const opUnitsAccess = await this.authService.getOpunitAccess(user.id, Number(body.opunitId));
    if (!opUnitsAccess) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'User does not have access to the opunit',
        data: [],
      });
    }
    // Update the active opunit to the JWT token
    const token = this.authService.generateJwtToken({
      id: user.id,
      name: user.name,
      activeOpUnite: String(body.opunitId),
      roleId: userData?.role?.id,
    });
    res.cookie('auth_token', token, {
      httpOnly: true,
    });
    return {
      status: 'success',
      message: 'Opunit changed successfully',
      data: null,
    };
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Get('get-my-module-access')
  async getMyModuleAccess(@Req() req: Request) {
    //Get Current Logged in User Id
    const user = req.user as JwtPayloadDto;
    if (!user?.id || !user?.activeOpUnite) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'Invalid access',
        data: [],
      });
    }
    const userData = await this.authService.getUser(user.id);
    // check if user has Administrator role
    if (user.activeOpUnite === 'APPLICATION') {
      console.log(userData?.role);
      if (userData?.role?.name === 'Administrator') {
        return {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: {
            modules: ['FULL_ACCESS'],
            groups: ['FULL_ACCESS'],
          },
        };
      } else if (userData?.role) {
        const userRoleAccess: Permission[] = await this.authService.getAccessByRole(userData?.role?.id);
        if (userRoleAccess?.length > 0) {
          // Prepare modules and groups based on permissions
          const modules: string[] = Array.from(
            new Set(
              userRoleAccess
                .map((permission: Permission) => permission.module)
                .filter((module: any) => module !== null && module !== undefined),
            ),
          );
          const groups: string[] = Array.from(
            new Set(
              userRoleAccess
                .map((permission: Permission) => permission.groups)
                .filter((group: any) => group !== null && group !== undefined),
            ),
          );
          return {
            status: 'success',
            message: 'User profile retrieved successfully',
            data: {
              modules,
              groups,
            },
          };
        } else {
          throw new UnauthorizedException({
            status: 'error',
            message: 'No permissions assigned to this role',
            data: [],
          });
        }
      } else {
        throw new UnauthorizedException({
          status: 'error',
          message: 'Invalid access',
          data: [],
        });
      }
    } else {
      const opUnitsAccess = await this.authService.getOpunitAccess(user.id, Number(user.activeOpUnite));
      if (!opUnitsAccess) {
        throw new UnauthorizedException({
          status: 'error',
          message: 'User does not have access to the opunit',
          data: [],
        });
      }

      //Pull All Distinct Permissions for the User on this Opunit
      const userRoleAccess: Permission[] = await this.authService.getAccessByOpunit(
        user.id,
        Number(user.activeOpUnite),
      );
      if (userRoleAccess?.length > 0) {
        // Prepare modules and groups based on permissions
        const modules: string[] = Array.from(
          new Set(
            userRoleAccess
              .map((permission: Permission) => permission.module)
              .filter((module: any) => module !== null && module !== undefined),
          ),
        );
        const groups: string[] = Array.from(
          new Set(
            userRoleAccess
              .map((permission: Permission) => permission.groups)
              .filter((group: any) => group !== null && group !== undefined),
          ),
        );
        return {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: {
            modules,
            groups,
          },
        };
      } else {
        throw new UnauthorizedException({
          status: 'error',
          message: 'No permissions assigned to this role',
          data: [],
        });
      }
    }
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Get('get-my-permissions-by-module/:module')
  async getPermissionByModule(@Req() req: Request, @Param('module') module: string) {
    //Get Current Logged in User Id
    const user = req.user as JwtPayloadDto;
    if (!user?.id || !user?.activeOpUnite) {
      throw new UnauthorizedException({
        status: 'error',
        message: 'Invalid access',
        data: [],
      });
    }
    const userData = await this.authService.getUser(user.id);
    if (user.activeOpUnite === 'APPLICATION') {
      if (userData?.role?.name === 'Administrator') {
        return {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: ['FULL_ACCESS'],
        };
      } else if (userData?.role) {
        const userRoleAccess: Permission[] = await this.authService.getPermissionsByUserModule(userData?.id, module);
        if (userRoleAccess?.length > 0) {
          const slugs: string[] = userRoleAccess.map((permission: Permission) => permission.slug);
          return {
            status: 'success',
            message: 'User profile retrieved successfully',
            data: slugs,
          };
        } else {
          throw new UnauthorizedException({
            status: 'error',
            message: 'No permissions assigned to this role',
            data: [],
          });
        }
      } else {
        throw new UnauthorizedException({
          status: 'error',
          message: 'Invalid access',
          data: [],
        });
      }
    } else {
      const opUnitsAccess = await this.authService.getOpunitAccess(user.id, Number(user.activeOpUnite));
      if (!opUnitsAccess) {
        throw new UnauthorizedException({
          status: 'error',
          message: 'User does not have access to the opunit',
          data: [],
        });
      }
      //Pull All Distinct Permissions for the User on this Opunit
      const userRoleAccess: Permission[] = await this.authService.getAccessByOpunitModule(
        user.id,
        Number(user.activeOpUnite),
        module,
      );
      if (userRoleAccess?.length > 0) {
        const slugs: string[] = userRoleAccess.map((permission: Permission) => permission.slug);
        return {
          status: 'success',
          message: 'User profile retrieved successfully',
          data: slugs,
        };
      } else {
        throw new UnauthorizedException({
          status: 'error',
          message: 'No permissions assigned to this role',
          data: [],
        });
      }
    }
  }
}
