import { Body, Controller, Get, Param, Post, Put, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MediaAssetUploadDTO } from './dto/media-asset-upload.dto';
import { MediaAssetService } from './media-asset.service';
import { ApiBody, ApiConsumes, ApiCookieAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CRBody, CRSuccessResponse, CRBadRequestResponse, CRInternalServerErrorResponse } from './api-doc';
import { AppConfig } from '@api/configs/app-config';

@ApiTags('Media Asset Management')
@Controller('/media-asset')
export class MediaAssetController {
  constructor(private readonly mediaAssetService: MediaAssetService) {}

  @ApiCookieAuth()
  @ApiOperation({ summary: 'Upload a media asset file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody(CRBody)
  @ApiResponse(CRSuccessResponse)
  @ApiResponse(CRBadRequestResponse)
  @ApiResponse(CRInternalServerErrorResponse)
  @Post()
  @UseInterceptors(
    FileInterceptor('file', {
      dest: AppConfig.getEnvPath('MEDIA_ASSET_FILE_UPLOAD_DIR'),
      limits: { fileSize: AppConfig.getNumber('MEDIA_ASSET_FILE_SIZE_LIMIT_IN_BYTES'), files: 1 },
    }),
  )
  async createMediaAsset(@UploadedFile() file: Express.Multer.File, @Body() body: MediaAssetUploadDTO) {
    const result = await this.mediaAssetService.createMediaAsset(file, body);
    return {
      status: 'success',
      message: 'Media asset created successfully',
      data: result,
    };
  }

  @ApiCookieAuth()
  @ApiOperation({ summary: 'Update a media asset file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody(CRBody)
  @ApiResponse(CRSuccessResponse)
  @ApiResponse(CRBadRequestResponse)
  @ApiResponse(CRInternalServerErrorResponse)
  @Put('/:mediaAssetId')
  @UseInterceptors(
    FileInterceptor('file', {
      dest: AppConfig.getEnvPath('MEDIA_ASSET_FILE_UPLOAD_DIR'),
      limits: { fileSize: AppConfig.getNumber('MEDIA_ASSET_FILE_SIZE_LIMIT_IN_BYTES'), files: 1 },
    }),
  )
  async updateMediaAsset(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: MediaAssetUploadDTO,
    @Param('mediaAssetId') mediaAssetId: number,
  ) {
    const result = await this.mediaAssetService.updateMediaAsset(mediaAssetId, file, body);
    return {
      status: 'success',
      message: 'Media asset created successfully',
      data: result,
    };
  }
  @ApiCookieAuth()
  @ApiOperation({ summary: 'Get a media asset by id' })
  @ApiResponse(CRSuccessResponse)
  @Get('/:mediaAssetId')
  async getMediaAsset(@Param('mediaAssetId') mediaAssetId: number) {
    const result = await this.mediaAssetService.getMediaAssetById(mediaAssetId);
    return {
      status: 'success',
      message: 'Media asset retrieved successfully',
      data: result,
    };
  }
}
