import { HttpStatus } from '@nestjs/common';

export const CRBody = {
  schema: {
    type: 'object',
    properties: {
      file: {
        type: 'string',
        format: 'binary',
      },
      folderName: {
        type: 'string',
      },
      fileType: {
        type: 'string',
        enum: ['image', 'video'],
      },
      metadata: {
        type: 'object',
      },
    },
    required: ['file', 'folderName', 'fileType'],
  },
};
export const CRSuccessResponse = {
  status: HttpStatus.OK,
  description: 'Media asset uploaded successfully',
  schema: {
    example: {
      status: 'success',
      message: 'Media asset created successfully',
      data: {
        id: 10,
        fileName: 'Image_17.jpg',
        fileURL: '/media-asset-uploads/353c7713b15480d3510abbbea8b94e3c_Image_17.jpg',
        thumbnailURL: '/media-asset-uploads/353c7713b15480d3510abbbea8b94e3c_Image_17.jpg',
        fileType: 'image',
        folderName: 'category',
        metadata: {},
      },
    },
  },
};
export const CRBadRequestResponse = {
  status: HttpStatus.BAD_REQUEST,
  description: 'Invalid input or missing file',
  schema: {
    example: {
      statusCode: 400,
      error: 'Bad Request',
      message: [
        {
          property: 'folderName',
          errors: ['Folder name must be a string', 'Folder name cannot be empty'],
        },
        {
          property: 'fileType',
          errors: ['File type must be either image or video', 'File type is required'],
        },
      ],
    },
  },
};

export const CRInternalServerErrorResponse = {
  status: HttpStatus.INTERNAL_SERVER_ERROR,
  description: 'Failed to process the file upload',
  schema: {
    example: {
      statusCode: 500,
      message: 'Internal server error',
    },
  },
};
