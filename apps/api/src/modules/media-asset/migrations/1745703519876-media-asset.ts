import { MigrationInterface, QueryRunner } from 'typeorm';

export class MediaAsset1745703519876 implements MigrationInterface {
  name = 'MediaAsset1745703519876';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`media_assets\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`file_name\` varchar(2048) NOT NULL,
                \`file_url\` varchar(2048) NOT NULL,
                \`thumbnail_url\` varchar(2048) NULL,
                \`file_type\` enum ('image', 'video') NOT NULL,
                \`folder_name\` varchar(512) NOT NULL,
                \`referenced_by\` json NOT NULL,
                \`metadata\` json NOT NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE \`media_assets\`
        `);
  }
}
