import { MigrationInterface, QueryRunner } from 'typeorm';
import { MEDIA_ASSET_TABLE_NAME } from '@api/modules/media-asset/entities/media-asset.entity';

export class MediaAsset_1751802450530 implements MigrationInterface {
  name = 'MediaAsset_1751802450530';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${MEDIA_ASSET_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`file_name\` varchar(256) NOT NULL,
                \`file_url\` varchar(1024) NOT NULL,
                \`thumbnail_url\` varchar(1024) NULL,
                \`file_type\` enum ('image', 'video') NOT NULL,
                \`folder_name\` varchar(64) NULL,
                \`metadata\` json NOT NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE \`${MEDIA_ASSET_TABLE_NAME}\`
        `);
  }
}
