import { IsIn, IsNotEmpty, IsString } from 'class-validator';

export class MediaAssetUploadDTO {
  @IsNotEmpty({ message: 'Folder name cannot be empty' })
  @IsString({ message: 'Folder name must be a string' })
  folderName: string;

  @IsNotEmpty({ message: 'File type is required' })
  @IsIn(['image', 'video'], { message: 'File type must be either image or video' })
  fileType: 'image' | 'video';

  referencedBy: string[];
  metadata: Record<string, any>;
}
