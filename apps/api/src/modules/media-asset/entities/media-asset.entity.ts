import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

export const MEDIA_ASSET_TABLE_NAME = 'media_assets';

@Entity({ name: MEDIA_ASSET_TABLE_NAME })
export class MediaAsset {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'file_name', type: 'varchar', length: 256 })
  fileName: string;

  @Column({ name: 'file_url', type: 'varchar', length: 1024 })
  fileURL: string;

  @Column({ name: 'thumbnail_url', type: 'varchar', length: 1024, nullable: true })
  thumbnailURL: string;

  @Column({ name: 'file_type', type: 'enum', enum: ['image', 'video'] })
  fileType: 'image' | 'video';

  @Column({ name: 'folder_name', type: 'varchar', length: 64, nullable: true })
  folderName: string;

  @Column({ name: 'metadata', type: 'json' })
  metadata: { [k: string]: any };
}
