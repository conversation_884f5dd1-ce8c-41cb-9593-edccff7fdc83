import { Injectable, NotFoundException } from '@nestjs/common';
import { MediaAssetUploadDTO } from './dto/media-asset-upload.dto';
import { AppConfig } from '@api/configs/app-config';
import * as path from 'path';
import { promises as fs } from 'fs';
import { InjectRepository } from '@nestjs/typeorm';
import { MediaAsset } from './entities/media-asset.entity';
import { Repository } from 'typeorm';

@Injectable()
export class MediaAssetService {
  constructor(
    @InjectRepository(MediaAsset)
    private mediaAssetRepository: Repository<MediaAsset>,
  ) {}
  async createMediaAsset(file: Express.Multer.File, body: MediaAssetUploadDTO) {
    const newFilePath = `${file.path}_${file.originalname.replace(/\s/g, '_')}`;
    await fs.rename(file.path, newFilePath);
    const relativePath = `/${path.relative(AppConfig.getEnvPath('PUBLIC_DIR'), newFilePath).replace(/\\/g, '/')}`;
    const mediaAsset = this.mediaAssetRepository.create({
      fileName: file.originalname,
      fileURL: relativePath,
      thumbnailURL: relativePath,
      fileType: body.fileType,
      folderName: body.folderName,
      metadata: body.metadata || {},
    });
    return await this.mediaAssetRepository.save(mediaAsset);
  }

  async getMediaAssetById(mediaAssetId: number) {
    const mediaAsset = await this.mediaAssetRepository.findOne({
      where: { id: mediaAssetId },
    });
    if (!mediaAsset) {
      throw new NotFoundException('Media asset not found');
    }
    return mediaAsset;
  }

  async updateMediaAsset(mediaAssetId: number, file: Express.Multer.File, body: MediaAssetUploadDTO) {
    const mediaAsset = await this.getMediaAssetById(mediaAssetId);
    const oldFile = path.join(AppConfig.getEnvPath('PUBLIC_DIR'), mediaAsset.fileURL);

    const newFilePath = `${file.path}_${file.originalname.replace(/\s/g, '_')}`;
    await fs.rename(file.path, newFilePath);
    const relativePath = `/${path.relative(AppConfig.getEnvPath('PUBLIC_DIR'), newFilePath).replace(/\\/g, '/')}`;

    mediaAsset.fileName = file.originalname;
    mediaAsset.fileURL = relativePath;
    mediaAsset.thumbnailURL = relativePath;
    mediaAsset.fileType = body.fileType;
    mediaAsset.folderName = body.folderName;
    mediaAsset.metadata = body.metadata || {};

    const updatedMediaAsset = await this.mediaAssetRepository.save(mediaAsset);
    const isFileExists = await fs
      .access(oldFile)
      .then(() => true)
      .catch(() => false);
    if (isFileExists) {
      await fs.rm(oldFile);
    }
    return updatedMediaAsset;
  }
}
