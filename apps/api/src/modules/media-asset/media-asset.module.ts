import { Module } from '@nestjs/common';
import { MediaAssetController } from './media-asset.controller';
import { MediaAssetService } from './media-asset.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaAsset } from './entities/media-asset.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MediaAsset])],
  controllers: [MediaAssetController],
  providers: [MediaAssetService],
})
export class MediaAssetModuleModule {}
