import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ProductType } from '../entities/product-type.entity';

@Injectable()
export class ProductTypeRepository extends Repository<ProductType> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(ProductType, dataSource.createEntityManager());
  }
}
