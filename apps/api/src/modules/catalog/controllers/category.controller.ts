import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiCookieAuth, ApiTags } from '@nestjs/swagger';
import { CategoryService } from '../services/category.service';
import { JwtAuthGuard } from '@api/modules/auth/guards/jwt-auth.guard';
import { ApiResponseType } from '@common-utils/api-types';
import { CreateCategoryDto } from '../dto/category/create-category.dto';
import { UpdateCategoryDto } from '../dto/category/update-category.dto';
import { AccessGuard } from '@api/modules/auth/guards/access.guard';
import { ListCategorySwaggerDoc } from './api-docs/category/list-category';
import { CategoryListDto } from '../dto/category/list-category.dto';
import { CategoryDetailsSwaggerDoc } from './api-docs/category/detail-category';
import { CreateCategorySwaggerDoc } from './api-docs/category/create-category';
import { UpdateCategorySwaggerDoc } from './api-docs/category/update-category';
import { deleteCategorySwaggerDocs } from './api-docs/category/delete-category';
@ApiTags('Categories')
@Controller('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSIon_NAME') // Replace with actual permission name
  @ApiCookieAuth()
  @Get()
  @ListCategorySwaggerDoc()
  async getCategoryList(@Query() query: CategoryListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit, parent_category_id } = query;
    const { data, total } = await this.categoryService.getCategoryList(keyword, page, limit, parent_category_id);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No categories found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Categories retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Get('/:categoryId')
  @CategoryDetailsSwaggerDoc()
  async getCategoryById(@Param('categoryId') categoryId: number): Promise<ApiResponseType<any>> {
    const category = await this.categoryService.getCategoryById(categoryId);
    if (!category) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Category retrieved successfully',
      data: category,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME') // Replace with actual permission name
  @ApiCookieAuth()
  @Post()
  @CreateCategorySwaggerDoc()
  async createCategory(@Body() createCategoryDto: CreateCategoryDto): Promise<ApiResponseType<any>> {
    // Check if the category name already exists
    const existingCategory = await this.categoryService.getCategoryByName(createCategoryDto.name);
    if (existingCategory) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    const category = await this.categoryService.createCategory(createCategoryDto);
    return {
      status: 'success',
      message: 'Category created successfully',
      data: category,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME') // Replace with actual permission name
  @ApiCookieAuth()
  @Put('/:categoryId')
  @UpdateCategorySwaggerDoc()
  async updateCategory(
    @Param('categoryId') categoryId: number,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<ApiResponseType<any>> {
    // Check if the category exists
    const existingCategory = await this.categoryService.getCategoryById(categoryId);
    if (!existingCategory) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    // Check if the updated category name already exists
    if (updateCategoryDto.name) {
      const existingCategoryByName = await this.categoryService.getCategoryByName(updateCategoryDto.name);
      if (existingCategoryByName && existingCategory.id !== categoryId) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Category name already exists',
          },
          HttpStatus.CONFLICT,
        );
      }
    }

    const updatedCategory = await this.categoryService.updateCategory(categoryId, updateCategoryDto);
    return {
      status: 'success',
      message: 'Category updated successfully',
      data: updatedCategory,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME') // Replace with actual permission name
  @ApiCookieAuth()
  @Delete('/:categoryId')
  @deleteCategorySwaggerDocs()
  async deleteCategory(@Param('categoryId') categoryId: number): Promise<ApiResponseType<any>> {
    // Check if the category exists
    const existingCategory = await this.categoryService.getCategoryById(categoryId);
    if (!existingCategory) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    await this.categoryService.deleteCategory(categoryId);
    return {
      status: 'success',
      message: 'Category deleted successfully',
      data: null,
    };
  }
}
