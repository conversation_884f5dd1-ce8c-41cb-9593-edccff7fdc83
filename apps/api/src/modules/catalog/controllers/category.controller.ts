import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiCookieAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CategoryService } from '../services/category.service';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { ApiResponseType } from '@/types/api-response';
import { CreateCategoryDto } from '../dto/category/create-category.dto';
import { UpdateCategoryDto } from '../dto/category/update-category.dto';

@ApiTags('Categories')
@Controller('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Get()
  @ApiOperation({ summary: 'Get All Categories list' })
  @ApiQuery({
    name: 'keyword',
    required: false,
    description: 'Keyword to filter categories by name',
  })
  @ApiResponse({
    status: 200,
    description: 'Categories retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Categories retrieved successfully',
        data: [
          {
            id: 1,
            name: 'Category Name',
            description: 'Category Description',
            slug: 'category-slug',
            categoryImage: [],
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No categories found',
    schema: {
      example: {
        status: 'error',
        message: 'No categories found',
        data: [],
      },
    },
  })
  async getCategoryList(@Query('keyword') keyword: string): Promise<ApiResponseType<any>> {
    const categories = await this.categoryService.getCategoryList(keyword ?? null);

    if (!categories || categories.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No categories found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Categories retrieved successfully',
      data: categories,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Get('/:categoryId')
  @ApiOperation({ summary: 'Get Category by ID' })
  @ApiResponse({
    status: 200,
    description: 'Category retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Category retrieved successfully',
        data: {
          id: 1,
          name: 'Category Name',
          description: 'Category Description',
          slug: 'category-slug',
          categoryImage: [],
          subCategories: [],
          catalogs: [],
          products: [],
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
    schema: {
      example: {
        status: 'error',
        message: 'Category not found',
      },
    },
  })
  async getCategoryById(@Query('categoryId') categoryId: number): Promise<ApiResponseType<any>> {
    const category = await this.categoryService.getCategoryById(categoryId);
    if (!category) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Category retrieved successfully',
      data: category,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Post()
  @ApiOperation({ summary: 'Create a new Category' })
  @ApiBody({
    description: 'Payload to create Category',
    schema: {
      example: {
        name: 'Demon Category name',
        description: 'Demo Description',
        categoryImageId: 5,
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Category created successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Category created successfully',
        data: {
          id: 1,
          name: 'New Category',
          description: 'New Category Description',
          slug: 'new_category',
          categoryImage: [],
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    schema: {
      example: {
        status: 'error',
        message: [
          {
            property: 'name',
            errors: ['Name cannot be empty.'],
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict: Opunit name already exists',
    schema: {
      example: {
        status: 'error',
        message: 'Opunit name already exists',
      },
    },
  })
  async createCategory(@Body() createCategoryDto: CreateCategoryDto): Promise<ApiResponseType<any>> {
    // Check if the category name already exists
    const existingCategory = await this.categoryService.getCategoryByName(createCategoryDto.name);
    if (existingCategory) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    const category = await this.categoryService.createCategory(createCategoryDto);
    return {
      status: 'success',
      message: 'Category created successfully',
      data: category,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Put('/:categoryId')
  @ApiOperation({ summary: 'Update a Category' })
  @ApiBody({
    description: 'Payload to update Category',
    schema: {
      example: {
        name: 'Updated Category Name',
        description: 'Updated Description',
        categoryImageId: 5,
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Category updated successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Category updated successfully',
        data: {
          id: 1,
          name: 'Updated Category Name',
          description: 'Updated Description',
          slug: 'updated_category_name',
          categoryImage: [],
          subCategories: [],
          catalogs: [],
          products: [],
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    schema: {
      example: {
        status: 'error',
        message: [
          {
            property: 'name',
            errors: ['Name cannot be empty.'],
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
    schema: {
      example: {
        status: 'error',
        message: 'Category not found',
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict: Category name already exists',
    schema: {
      example: {
        status: 'error',
        message: 'Category name already exists',
      },
    },
  })
  async updateCategory(
    @Param('categoryId') categoryId: number,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<ApiResponseType<any>> {
    // Check if the category exists
    const existingCategory = await this.categoryService.getCategoryById(categoryId);
    if (!existingCategory) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    // Check if the updated category name already exists
    if (updateCategoryDto.name) {
      const existingCategoryByName = await this.categoryService.getCategoryByName(updateCategoryDto.name);
      if (existingCategoryByName && existingCategory.id !== categoryId) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Category name already exists',
          },
          HttpStatus.CONFLICT,
        );
      }
    }

    const updatedCategory = await this.categoryService.updateCategory(categoryId, updateCategoryDto);
    return {
      status: 'success',
      message: 'Category updated successfully',
      data: updatedCategory,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Delete('/:categoryId')
  @ApiOperation({ summary: 'Delete a Category' })
  @ApiResponse({
    status: 200,
    description: 'Category deleted successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Category deleted successfully',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
    schema: {
      example: {
        status: 'error',
        message: 'Category not found',
      },
    },
  })
  async deleteCategory(@Param('categoryId') categoryId: number): Promise<ApiResponseType<any>> {
    // Check if the category exists
    const existingCategory = await this.categoryService.getCategoryById(categoryId);
    if (!existingCategory) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    await this.categoryService.deleteCategory(categoryId);
    return {
      status: 'success',
      message: 'Category deleted successfully',
      data: null,
    };
  }
}
