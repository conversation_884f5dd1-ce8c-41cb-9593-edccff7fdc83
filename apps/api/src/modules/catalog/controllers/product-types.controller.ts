import { Controller, Get, NotFoundException, Param } from '@nestjs/common';
import { ProductTypesService } from '../services/product-types.service';

@Controller('product-types')
export class ProductTypesController {
  constructor(private readonly productTypesService: ProductTypesService) {}

  @Get()
  async getAllProductTypes() {
    const productTypes = await this.productTypesService.findAll();
    return {
      status: 'success',
      data: productTypes,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const productType = await this.productTypesService.findOne(+id);
    if (!productType) {
      throw new NotFoundException();
    }
    return { status: 'success', data: productType };
  }
}
