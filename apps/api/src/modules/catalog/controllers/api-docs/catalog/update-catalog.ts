import { ApiB<PERSON>, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function UpdateCatalogSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Update catalog by id' }),
    ApiBody({
      description: 'Catalog update payload',
      schema: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            example: 'Updated Catalog Name',
            description: 'Updated name of the catalog',
          },
          description: {
            type: 'string',
            example: 'Updated description of the catalog',
            description: 'Updated description of the catalog',
          },
          categoryIds: {
            type: 'array',
            items: {
              type: 'number',
            },
            example: [1, 2],
            description: 'Array of category IDs associated with the updated catalog',
          },
          productIds: {
            type: 'array',
            items: {
              type: 'number',
            },
            example: [1, 2],
            description: 'Array of product IDs associated with the updated catalog',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Catalog updated successfully.',
      schema: {
        example: {
          statusCode: 200,
          message: 'Catalog updated successfully.',
          data: {
            id: 1,
            name: 'Updated Catalog Name',
            description: 'Updated description of the catalog',
            slug: 'updated-catalog-name',
            categories: '<array>',
            products: '<array>',
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Catalog not found. The specified ID does not exist in the database.',
      schema: {
        example: {
          statusCode: 404,
          error: 'Not Found',
          message: 'Catalog with the specified ID does not exist in the database.',
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict error, e.g., duplicate name .',
      schema: {
        example: {
          statusCode: 409,
          error: 'Conflict',
          message: 'Name already exists .',
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Validation failed for the input data',
      schema: {
        example: {
          statusCode: 400,
          error: 'Bad Request',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),

    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
