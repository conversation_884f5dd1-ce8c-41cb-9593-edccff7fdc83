import { ApiQ<PERSON>y, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function CatalogListSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get catalog list' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to search for catalog',
      type: 'string',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      type: 'number',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page',
      type: 'number',
      example: 10,
    }),
    ApiResponse({
      status: 200,
      description: 'Catalog list retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Catalog list retrieved successfully',
          data: {
            items: [
              {
                id: '<number>',
                name: '<string>',
                description: '<string>',
                categories: [
                  {
                    id: '<number>',
                    name: '<string>',
                  },
                ],
                products: [
                  {
                    id: '<number>',
                    name: '<string>',
                  },
                ],
              },
            ],
            total: '<number>',
            page: '<number>',
            limit: '<number>',
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'No Catalog found',
      schema: {
        example: {
          status: 'error',
          message: 'No data found',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
