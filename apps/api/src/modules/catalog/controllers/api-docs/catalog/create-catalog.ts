import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';

export function CreateCatalogSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new catalog' }),
    ApiBody({
      description: 'Person creation payload',
      schema: {
        type: 'object',
        required: ['name'],
        properties: {
          name: {
            type: 'string',
            example: '<PERSON>',
            description: 'Name of the person',
          },
          description: {
            type: 'string',
            example: 'A brief description about the catalog',
            description: 'Description of the person',
          },
          categoryIds: {
            type: 'array',
            items: {
              type: 'number',
            },
            example: [1, 2],
            description: 'Array of category IDs associated with the catalog',
          },
          productIds: {
            type: 'array',
            items: {
              type: 'number',
            },
            example: [1, 2],
            description: 'Array of product IDs associated with the catalog',
          },
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict error, e.g., duplicate name in the same group',
      schema: {
        example: {
          statusCode: 409,
          error: 'Conflict',
          message: 'Name already exists in the specified group.',
        },
      },
    }),
    ApiResponse({
      status: 500,
      description: 'Internal server error',
      schema: {
        example: {
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Failed to create person due to server error.',
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Catalog created successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Catalog created successfully',
          data: {
            id: 1,
            name: 'John Doe',
            description: 'A brief description about the catalog',
            slug: 'catalog_slug',
            categories: '<array>',
            products: '<array>',
          },
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
