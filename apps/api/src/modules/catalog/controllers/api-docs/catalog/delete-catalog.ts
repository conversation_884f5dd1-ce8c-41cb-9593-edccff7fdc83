import { Api<PERSON>aram, ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function deleteCatalogSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete catalog by id' }),
    ApiParam({
      name: 'id',
      required: true,
      description: 'ID of the catalog to delete',
      type: 'number',
    }),
    ApiResponse({
      status: 200,
      description: 'Catalog deleted successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Catalog deleted successfully.',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Catalog not found',
      schema: {
        example: {
          status: 'error',
          message: 'Catalog not found',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
