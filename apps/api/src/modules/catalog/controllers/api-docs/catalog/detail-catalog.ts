import { applyDecorators } from '@nestjs/common';
import { ApiParam, ApiOperation, ApiResponse } from '@nestjs/swagger';

export function CataloDetailsSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get Catalog Details by catalog Id' }),
    ApiResponse({
      status: 200,
      description: 'Catalog details retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Catalog details retrieved successfully.',
          data: {
            id: '<number>',
            name: '<string>',
            description: '<string>',
            slug: '<string>',
            categories: [
              {
                id: '<number>',
                name: '<string>',
              },
            ],
            products: [
              {
                id: '<number>',
                name: '<string>',
              },
            ],
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Catalog not found',
      schema: {
        example: {
          status: 'error',
          message: 'Catalog not found',
          data: null,
        },
      },
    }),
    ApiParam({
      name: 'catalogId',
      required: true,
      description: 'ID of the catalog to retrieve',
      type: 'number',
    } as any),

    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
