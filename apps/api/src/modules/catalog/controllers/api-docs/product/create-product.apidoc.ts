import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function CreateProductApiDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new product' }),
    ApiResponse({ status: HttpStatus.CREATED, description: 'Product created successfully' }),
    ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid product data' }),
    ApiResponse({
      status: HttpStatus.NOT_FOUND,
      description: 'Invalid product type',
      example: {
        message: 'Product type with ID 0 not found',
        error: 'Not Found',
        statusCode: 404,
      },
    }),
  );
}
