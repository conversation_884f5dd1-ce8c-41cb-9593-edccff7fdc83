import { ApiParam, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function CategoryDetailsSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Get Category by ID' }),
    ApiParam({
      name: 'categoryId',
      required: true,
      description: 'ID of the category to retrieve',
    }),
    ApiResponse({
      status: 200,
      description: 'Category retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Category retrieved successfully',
          data: {
            id: 1,
            name: 'Category Name',
            description: 'Category Description',
            slug: 'category-slug',
            categoryImage: [],
            parent: {
              id: 1,
              name: 'Parent Category Name',
              slug: 'parent-category-slug',
            },
            subCategories: [],
            catalogs: [],
            products: [],
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Category not found',
      schema: {
        example: {
          status: 'error',
          message: 'Category not found',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
