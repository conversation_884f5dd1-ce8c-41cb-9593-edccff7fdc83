import { ApiQuery, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ListCategorySwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Get All Categories list' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to filter categories by name',
    }),
    ApiQuery({
      name: 'page',
      required: false,
      description: 'Page number for pagination',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: false,
      description: 'Number of items per page for pagination',
      example: 10,
    }),
    ApiQuery({
      name: 'parent_category_id',
      required: false,
      description: 'Parent category ID to filter categories',
      example: 1,
    }),
    ApiResponse({
      status: 200,
      description: 'Categories retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Categories retrieved successfully',
          data: [
            {
              id: 1,
              name: 'Category Name',
              description: 'Category Description',
              slug: 'category-slug',
              categoryImage: [],
              parent: {
                id: 1,
                name: 'Parent Category Name',
              },
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'No categories found',
      schema: {
        example: {
          status: 'error',
          message: 'No categories found',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
