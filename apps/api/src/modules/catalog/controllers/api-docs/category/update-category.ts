import { ApiParam, ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function UpdateCategorySwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Update a category' }),
    ApiParam({
      name: 'categoryId',
      required: true,
      description: 'ID of the category to update',
    }),
    ApiBody({
      description: 'Category data to be updated',
      schema: {
        example: {
          name: 'Updated Category',
          description: 'Updated description of the category',
          slug: 'updated-category',
          parentCategoryId: 1,
          categoryImageId: 1,
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Category updated successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Category updated successfully',
          data: {
            id: 1,
            name: 'Updated Category',
            description: 'Updated description of the category',
            slug: 'updated-category',
            parentCategory: '<array>',
            categoryImage: '<array>',
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Bad Request',
      schema: {
        example: {
          status: 'error',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Category not found',
      schema: {
        example: {
          status: 'error',
          message: 'Error while updating category with id = 1. Error message = Category not found',
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict. Error  Category name already exists',
      schema: {
        example: {
          status: 'error',
          message: 'Error while updating category  Category name already exists',
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
