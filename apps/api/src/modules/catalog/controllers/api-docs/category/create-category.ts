import { ApiBody, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function CreateCategorySwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new category' }),
    ApiBody({
      description: 'Category data to be created',
      schema: {
        example: {
          name: 'New Category',
          description: 'Description of the new category',
          slug: 'new-category',
          parentCategoryId: 1,
          categoryImageId: 1,
        },
      },
    }),
    ApiResponse({
      status: 201,
      description: 'Category created successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Category created successfully',
          data: {
            id: 1,
            name: 'New Category',
            description: 'Description of the new category',
            slug: 'new-category',
            parentCategory: [],
            categoryImage: [],
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Bad Request',
      schema: {
        example: {
          status: 'error',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict: Category name already exists',
      schema: {
        example: {
          status: 'error',
          message: 'Category name already exists',
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
