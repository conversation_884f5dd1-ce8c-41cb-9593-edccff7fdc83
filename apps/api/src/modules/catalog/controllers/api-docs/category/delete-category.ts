import { ApiParam, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function deleteCategorySwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete a Category bu Category Id' }),

    ApiParam({
      name: 'categoryId',
      required: true,
      description: 'ID of the Category to delete',
      type: 'number',
    }),

    ApiResponse({
      status: 200,
      description: 'Category deleted successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Category deleted successfully.',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Category not found',
      schema: {
        example: {
          status: 'error',
          message: 'Category not found',
          data: null,
        },
      },
    }),
  );
}
