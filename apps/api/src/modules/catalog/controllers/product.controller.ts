import { Body, Controller, Delete, Get, HttpStatus, Param, Post, Put } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { ProductCUDTO } from '../dto/product/product-cu-dto';
import { ProductService } from '../services/product.service';

@ApiTags('Products')
@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get()
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Products retrieved successfully' })
  async getProducts() {
    return Promise.resolve({
      status: 'success',
      message: 'Products retrieved successfully',
      data: [],
    });
  }

  @Get('/:productId')
  @ApiOperation({ summary: 'Get product by ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Product retrieved successfully' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Product not found' })
  async getProductById(@Param('productId') _productId: string) {
    // TODO: Implement getProductById method in ProductService
    return Promise.resolve({
      status: 'success',
      message: 'Product retrieved successfully',
      data: [],
    });
  }

  @Post()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiBody({ type: ProductCUDTO })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Product created successfully' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid product data' })
  async createProduct(@Body() productData: ProductCUDTO) {
    const createdProduct = await this.productService.createProduct(productData);

    return {
      status: 'success',
      message: 'Product created successfully',
      data: createdProduct,
    };
  }

  @Put('/:productId')
  @ApiOperation({ summary: 'Update a product' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Product updated successfully' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Product not found' })
  async updateProduct(@Param('productId') _productId: string) {
    // TODO: Implement updateProduct method in ProductService
    return Promise.resolve({
      status: 'success',
      message: 'Product updated successfully',
      data: [],
    });
  }

  @Delete('/:productId')
  @ApiOperation({ summary: 'Delete a product' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Product deleted successfully' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Product not found' })
  async deleteProduct(@Param('productId') _productId: string) {
    // TODO: Implement deleteProduct method in ProductService
    return Promise.resolve({
      status: 'success',
      message: 'Product deleted successfully',
      data: [],
    });
  }
}
