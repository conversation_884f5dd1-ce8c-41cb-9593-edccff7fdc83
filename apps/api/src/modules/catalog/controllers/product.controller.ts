import { Body, Controller, Delete, Get, Post, Put } from '@nestjs/common';
import { ProductCUDTO } from '../dto/product/product-cu-dto';

@Controller('products')
export class ProductController {
  constructor() {}

  @Get()
  async getProducts() {
    return Promise.resolve({
      status: 'success',
      message: 'Products retrieved successfully',
      data: [],
    });
  }
  @Get('/:productId')
  async getProductById() {
    return Promise.resolve({
      status: 'success',
      message: 'Product retrieved successfully',
      data: [],
    });
  }
  @Post()
  async createProduct(@Body() product: ProductCUDTO) {
    return Promise.resolve({
      status: 'success',
      message: 'Product created successfully',
      data: [],
    });
  }
  @Put('/:productId')
  async updateProduct() {
    return Promise.resolve({
      status: 'success',
      message: 'Product updated successfully',
      data: [],
    });
  }
  @Delete('/:productId')
  async deleteProduct() {
    return Promise.resolve({
      status: 'success',
      message: 'Product deleted successfully',
      data: [],
    });
  }
}
