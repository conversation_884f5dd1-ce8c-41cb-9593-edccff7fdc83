import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductVariation } from '../entities/product-variation.entity';
import { Like, Repository } from 'typeorm';
import { ResourceModel } from '@common-utils/resource-type';

@ApiTags('Resource')
@Controller('/resource/product-variation')
export class ProductVariationResourceController {
  constructor(
    @InjectRepository(ProductVariation)
    private readonly repository: Repository<ProductVariation>,
  ) {}
  @Get()
  async list(@Query('search') search: string): Promise<ResourceModel[]> {
    const resources = await this.repository.find({
      select: { name: true, id: true },
      where: [{ name: Like(`%${search}%`) }],
      take: 15,
    });
    return resources.map((resource) => ({ identifier: resource.id, displayText: resource.name }));
  }
}
