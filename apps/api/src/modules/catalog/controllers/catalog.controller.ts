import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiCookieAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CatalogService } from '../services/catalog.service';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { ApiResponseType } from '@/types/api-response';
import { CreateCatalogDto } from '../dto/catalog/create-catalog.dto';
import { UpdateCatalogDto } from '../dto/catalog/update-catalog.dto';

@ApiTags('catalogs')
@Controller('catalog')
export class CatalogController {
  constructor(private readonly catalog: CatalogService) {}

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Get()
  @ApiOperation({ summary: 'Get All Catalogs list' })
  @ApiQuery({
    name: 'keyword',
    required: false,
    description: 'Keyword to filter catalogs by name',
  })
  @ApiResponse({
    status: 200,
    description: 'Catalogs retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Catalogs retrieved successfully',
        data: [
          {
            id: 1,
            name: 'Catalog Name',
            description: 'Catalog Description',
            slug: 'catalog_slug',
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No catalogs found',
    schema: {
      example: {
        status: 'error',
        message: 'No catalogs found',
        data: [],
      },
    },
  })
  async getCatalogs(@Query('keyword') keyword: string): Promise<ApiResponseType<any>> {
    const catalogs = await this.catalog.getCatalogs(keyword);
    if (!catalogs || catalogs.length === 0) {
      return {
        status: 'error',
        message: 'No catalogs found',
        data: [],
      };
    }
    return {
      status: 'success',
      message: 'Catalogs retrieved successfully',
      data: catalogs,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Get('/:catalogId')
  @ApiOperation({ summary: 'Get Catalog by ID' })
  @ApiQuery({
    name: 'catalogId',
    required: true,
    description: 'ID of the catalog to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'Catalog retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Catalog retrieved successfully',
        data: {
          id: 1,
          name: 'Catalog Name',
          description: 'Catalog Description',
          slug: 'catalog_slug',
          categories: [],
          products: [],
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Catalog not found',
    schema: {
      example: {
        status: 'error',
        message: 'Catalog not found',
        data: null,
      },
    },
  })
  async getCatalogById(@Query('catalogId') catalogId: number): Promise<ApiResponseType<any>> {
    const catalog = await this.catalog.getCatalogById(catalogId);
    if (!catalog) {
      return {
        status: 'error',
        message: 'Catalog not found',
        data: null,
      };
    }
    return {
      status: 'success',
      message: 'Catalog retrieved successfully',
      data: catalog,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Post()
  @ApiOperation({ summary: 'Create a new Catalog' })
  @ApiBody({
    description: 'Catalog data to create a new catalog',
    schema: {
      example: {
        name: 'Catalog Name',
        description: 'Catalog Description',
        categoryIds: [1, 2],
        productIds: [1, 2],
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Catalog created successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Catalog created successfully',
        data: {
          id: 1,
          name: 'Catalog Name',
          description: 'Catalog Description',
          slug: 'catalog_slug',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    schema: {
      example: {
        status: 'error',
        message: [
          {
            property: 'name',
            errors: ['Name cannot be empty.'],
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict: Opunit name already exists',
    schema: {
      example: {
        status: 'error',
        message: 'Opunit name already exists',
      },
    },
  })
  async createCatalog(@Body() catalogData: CreateCatalogDto): Promise<ApiResponseType<any>> {
    const existingCatalog = await this.catalog.getCatalogByName(catalogData.name);
    if (existingCatalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    const catalog = await this.catalog.createCatalog(catalogData);
    return {
      status: 'success',
      message: 'Catalog created successfully',
      data: catalog,
    };
  }

  @UseGuards(JwtAuthGuard) //Have to Apply Access Guard
  @ApiCookieAuth()
  @Put('/:catalogId')
  @ApiOperation({ summary: 'Update Catalog by ID' })
  @ApiBody({
    description: 'Catalog data to update the catalog',
    schema: {
      example: {
        name: 'Updated Catalog Name',
        description: 'Updated Catalog Description',
        categoryIds: [1, 2],
        productIds: [1, 2],
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Catalog updated successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Catalog updated successfully',
        data: {
          id: 1,
          name: 'Updated Catalog Name',
          description: 'Updated Catalog Description',
          slug: 'catalog_slug',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Catalog not found',
    schema: {
      example: {
        status: 'error',
        message: 'Catalog not found',
        data: null,
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    schema: {
      example: {
        status: 'error',
        message: [
          {
            property: 'name',
            errors: ['Name cannot be empty.'],
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict: Category name already exists',
    schema: {
      example: {
        status: 'error',
        message: 'Catalog name already exists',
      },
    },
  })
  async updateCatalog(
    @Param('catalogId') catalogId: number,
    @Body() catalogData: UpdateCatalogDto,
  ): Promise<ApiResponseType<any>> {
    const existingCatalog = await this.catalog.getCatalogById(catalogId);
    if (!existingCatalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Catalog not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    if (catalogData.name) {
      const existingCatalogByName = await this.catalog.getCatalogByName(catalogData.name);

      if (existingCatalogByName && existingCatalog.id != catalogId) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Catalog name already exists',
          },
          HttpStatus.CONFLICT,
        );
      }
    }
    const updatedcatalog = await this.catalog.updateCatalog(catalogId, catalogData);
    return {
      status: 'success',
      message: 'Catalog Updated Successfully',
      data: updatedcatalog,
    };
  }

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @Delete('/:catalogId')
  @ApiOperation({ summary: 'Delete Catalog by ID' })
  @ApiResponse({
    status: 200,
    description: 'Catalog deleted successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Catalog deleted successfully',
        data: null,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Catalog not found',
    schema: {
      example: {
        status: 'error',
        message: 'Catalog not found',
        data: null,
      },
    },
  })
  async deleteCatalog(@Param('catalogId') catalogId: number): Promise<ApiResponseType<any>> {
    const existingCatalog = await this.catalog.getCatalogById(catalogId);
    if (!existingCatalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Catalog not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.catalog.deleteCatalog(catalogId);
    return {
      status: 'success',
      message: 'Catalog deleted successfully',
      data: null,
    };
  }
}
