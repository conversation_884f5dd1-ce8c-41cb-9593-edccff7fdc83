import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiCookieAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CatalogService } from '../services/catalog.service';
import { JwtAuthGuard } from '@api/modules/auth/guards/jwt-auth.guard';
import { ApiResponseType } from '@common-utils/api-types';
import { CreateCatalogDto } from '../dto/catalog/create-catalog.dto';
import { UpdateCatalogDto } from '../dto/catalog/update-catalog.dto';
import { CatalogListSwaggerDocs } from './api-docs/catalog/list-catalog';
import { CatalogListDto } from '../dto/catalog/list-catalog.dto';
import { AccessGuard } from '@api/modules/auth/guards/access.guard';
import { CataloDetailsSwaggerDocs } from './api-docs/catalog/detail-catalog';
import { CreateCatalogSwaggerDocs } from './api-docs/catalog/create-catalog';
import { UpdateCatalogSwaggerDocs } from './api-docs/catalog/update-catalog';
import { deleteCatalogSwaggerDocs } from './api-docs/catalog/delete-catalog';

@ApiTags('catalogs')
@Controller('catalog')
export class CatalogController {
  constructor(private readonly catalog: CatalogService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME')
  @ApiCookieAuth()
  @CatalogListSwaggerDocs()
  @Get()
  async getCatalogs(@Query() query: CatalogListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.catalog.getCatalogs(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Catalogs retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME')
  @ApiCookieAuth()
  @Get('/:catalogId')
  @CataloDetailsSwaggerDocs()
  async getCatalogById(@Param('catalogId') catalogId: string): Promise<ApiResponseType<any>> {
    const catalog = await this.catalog.getCatalogById(+catalogId);
    if (!catalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Catalog retrieved successfully',
      data: catalog,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME')
  @ApiCookieAuth()
  @Post()
  @CreateCatalogSwaggerDocs()
  async createCatalog(@Body() catalogData: CreateCatalogDto): Promise<ApiResponseType<any>> {
    const existingCatalog = await this.catalog.getCatalogByName(catalogData.name);
    if (existingCatalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Category name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    const catalog = await this.catalog.createCatalog(catalogData);
    return {
      status: 'success',
      message: 'Catalog created successfully',
      data: catalog,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME')
  @ApiCookieAuth()
  @Put('/:catalogId')
  @ApiOperation({ summary: 'Update Catalog by ID' })
  @UpdateCatalogSwaggerDocs()
  async updateCatalog(
    @Param('catalogId') catalogId: string,
    @Body() catalogData: UpdateCatalogDto,
  ): Promise<ApiResponseType<any>> {
    const existingCatalog = await this.catalog.getCatalogById(+catalogId);
    if (!existingCatalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Catalog not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    if (catalogData.name) {
      const existingCatalogByName = await this.catalog.getCatalogByName(catalogData.name);

      if (existingCatalogByName && existingCatalog.id != +catalogId) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Catalog name already exists',
          },
          HttpStatus.CONFLICT,
        );
      }
    }
    const updatedcatalog = await this.catalog.updateCatalog(+catalogId, catalogData);
    return {
      status: 'success',
      message: 'Catalog Updated Successfully',
      data: updatedcatalog,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  // @Permissions('PERMISSION_NAME')
  @ApiCookieAuth()
  @Delete('/:catalogId')
  @deleteCatalogSwaggerDocs()
  async deleteCatalog(@Param('catalogId') catalogId: string): Promise<ApiResponseType<any>> {
    const existingCatalog = await this.catalog.getCatalogById(+catalogId);
    if (!existingCatalog) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Catalog not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.catalog.deleteCatalog(+catalogId);
    return {
      status: 'success',
      message: 'Catalog deleted successfully',
      data: null,
    };
  }
}
