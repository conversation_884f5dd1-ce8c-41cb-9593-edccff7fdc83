import { Injectable } from '@nestjs/common';
import { ProductType } from '../entities/product-type.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ProductTypesService {
  constructor(
    @InjectRepository(ProductType)
    private readonly productTypeRepository: Repository<ProductType>,
  ) {}

  findAll(): Promise<ProductType[]> {
    return this.productTypeRepository.find();
  }

  findOne(id: number) {
    return this.productTypeRepository.findOne({
      where: { id },
      relations: {
        attributes: true,
      },
    });
  }

  remove(id: number) {
    return `This action removes a #${id} productType`;
  }
}
