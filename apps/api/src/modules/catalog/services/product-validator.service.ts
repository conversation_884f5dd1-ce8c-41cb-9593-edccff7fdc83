import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductType } from '../entities/product_type/product-type.entity';
import { ProductTypeAttribute } from '../entities/product_type/product-type-attribute.entity';
import { Product } from '../entities/product.entity';

@Injectable()
export class ProductValidatorService {
  constructor(
    @InjectRepository(ProductType)
    private readonly productTypeRepository: Repository<ProductType>,
    @InjectRepository(ProductTypeAttribute)
    private readonly productTypeAttributeRepository: Repository<ProductTypeAttribute>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
  ) {}

  /**
   * Validate if a product type exists
   * @param productTypeId The ID of the product type to validate
   * @returns The product type if it exists
   * @throws NotFoundException if the product type does not exist
   */
  async validateProductType(productTypeId: number): Promise<ProductType> {
    const productType = await this.productTypeRepository.findOne({
      where: { id: productTypeId },
    });

    if (!productType) {
      throw new NotFoundException(`Product type with ID ${productTypeId} not found`);
    }

    return productType;
  }

  /**
   * Validate if a product attribute exists and belongs to the specified product type
   * @param attributeId The ID of the attribute to validate
   * @param productTypeId The ID of the product type the attribute should belong to
   * @returns The product type attribute if it exists and belongs to the product type
   * @throws NotFoundException if the attribute does not exist or does not belong to the product type
   */
  async validateProductAttribute(attributeId: number, productTypeId: number): Promise<ProductTypeAttribute> {
    const attribute = await this.productTypeAttributeRepository.findOne({
      where: { id: attributeId },
      relations: ['productType'],
    });

    if (!attribute) {
      throw new NotFoundException(`Product attribute with ID ${attributeId} not found`);
    }

    if (attribute.productType.id !== productTypeId) {
      throw new NotFoundException(
        `Product attribute with ID ${attributeId} does not belong to product type with ID ${productTypeId}`,
      );
    }

    return attribute;
  }

  /**
   * Validate if a slug is unique
   * @param slug The slug to validate
   * @returns true if the slug is unique, false otherwise
   */
  async isSlugUnique(slug: string): Promise<boolean> {
    const product = await this.productRepository.findOne({
      where: { slug },
    });

    return !product;
  }
}
