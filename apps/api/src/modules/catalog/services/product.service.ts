import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { ProductAttribute } from '../entities/product-attribute.entity';
import { ProductVariation } from '../entities/product-variation.entity';
import { ProductVariationAttribute } from '../entities/product-variation-attribute.entity';
import { ProductCUDTO } from '../dto/product/product-cu-dto';
import { ProductValidatorService } from './product-validator.service';
import { ProductTypeAttribute } from '../entities/product_type/product-type-attribute.entity';

@Injectable()
export class ProductService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductAttribute)
    private readonly productAttributeRepository: Repository<ProductAttribute>,
    @InjectRepository(ProductVariation)
    private readonly productVariationRepository: Repository<ProductVariation>,
    @InjectRepository(ProductVariationAttribute)
    private readonly productVariationAttributeRepository: Repository<ProductVariationAttribute>,
    @InjectRepository(ProductTypeAttribute)
    private readonly productTypeAttributeRepository: Repository<ProductTypeAttribute>,
    private readonly productValidatorService: ProductValidatorService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Create a new product with its attributes and variations
   * @param productData The product data to create
   * @returns The created product
   */
  async createProduct(productData: ProductCUDTO): Promise<Product> {
    // Validate slug uniqueness
    const isSlugUnique = await this.productValidatorService.isSlugUnique(productData.slug);
    if (!isSlugUnique) {
      throw new BadRequestException(`Product with slug '${productData.slug}' already exists`);
    }

    // Validate product type
    const productType = await this.productValidatorService.validateProductType(productData.productTypeId);

    // Start a transaction to ensure all operations succeed or fail together
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the product
      const product = this.productRepository.create({
        name: productData.name,
        description: productData.description,
        slug: productData.slug,
        barcode: productData.barcode,
        productType: productType,
      });

      // Save the product
      const savedProduct = await queryRunner.manager.save(product);

      // Process product attributes if any
      if (productData?.productAttributes?.length) {
        for (const attributeData of productData.productAttributes) {
          // Validate the attribute
          const attribute = await this.productValidatorService.validateProductAttribute(
            attributeData.attributeId,
            productData.productTypeId,
          );

          // Create and save the product attribute
          const productAttribute = this.productAttributeRepository.create({
            product: savedProduct,
            attribute: attribute,
            attributeValue: attributeData.attributeValue,
            metadata: attributeData.metadata,
          });

          await queryRunner.manager.save(productAttribute);
        }
      }

      // Process product variations if any
      if (productData?.productVariations?.length) {
        for (const variationData of productData.productVariations) {
          // Create and save the product variation
          const productVariation = this.productVariationRepository.create({
            name: variationData.name,
            basePrice: variationData.basePrice,
            skucode: variationData.skucode,
            product: savedProduct,
          });

          const savedVariation = await queryRunner.manager.save(productVariation);

          // Process variation attributes if any
          if (variationData?.attributes?.length) {
            for (const attributeData of variationData.attributes) {
              // Validate the attribute
              const attribute = await this.productValidatorService.validateProductAttribute(
                attributeData.attributeId,
                productData.productTypeId,
              );

              // Verify that this is a variant attribute
              const isVariantAttribute = await this.productTypeAttributeRepository.findOne({
                where: { id: attributeData.attributeId, isVariantAttribute: true },
              });

              if (!isVariantAttribute) {
                throw new BadRequestException(
                  `Attribute with ID ${attributeData.attributeId} is not a variant attribute`,
                );
              }

              // Create and save the product variation attribute
              const variationAttribute = this.productVariationAttributeRepository.create({
                productVariation: savedVariation,
                attribute: attribute,
                attributeValue: attributeData.attributeValue,
                metadata: attributeData.metadata,
              });

              await queryRunner.manager.save(variationAttribute);
            }
          }
        }
      }

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the created product with all relations
      const createdProduct = await this.productRepository.findOne({
        where: { id: savedProduct.id },
        relations: [
          'productType',
          'attributes',
          'attributes.attribute',
          'variations',
          'variations.attributes',
          'variations.attributes.attribute',
        ],
      });

      if (!createdProduct) {
        throw new InternalServerErrorException('Failed to retrieve the created product');
      }

      return createdProduct;
    } catch (error: any) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      if (error instanceof BadRequestException) {
        throw error;
      }

      const errorMessage = error.message || 'Unknown error';
      throw new InternalServerErrorException(`Failed to create product: ${errorMessage}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }
}
