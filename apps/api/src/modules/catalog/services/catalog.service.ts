import { Injectable } from '@nestjs/common';
import { Catalog } from '../entities/catalog.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Like, Repository } from 'typeorm';
import { CreateCatalogDto } from '../dto/catalog/create-catalog.dto';
import { UpdateCatalogDto } from '../dto/catalog/update-catalog.dto';
import { Category } from '../entities/category.entity';
import { Product } from '../entities/product.entity';

@Injectable()
export class CatalogService {
  constructor(
    @InjectRepository(Catalog)
    private readonly catalogRepository: Repository<Catalog>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
  ) {}

  async getCatalogs(keyword: string | null = null): Promise<Partial<Catalog>[]> {
    let whereCondition = undefined;
    if (keyword) {
      whereCondition = {
        name: Like(`%${keyword}%`),
        description: Like(`%${keyword}%`),
      };
    }

    return await this.catalogRepository.find({ where: whereCondition });
  }

  async getCatalogById(id: number): Promise<Catalog | null> {
    return await this.catalogRepository.findOne({
      where: { id },
      relations: ['categories', 'products'],
    });
  }

  async getCatalogByName(name: string): Promise<Catalog | null> {
    return await this.catalogRepository.findOne({ where: { name } });
  }

  async createCatalog(catalogData: CreateCatalogDto): Promise<Catalog> {
    const categories = catalogData.categoryIds
      ? await this.categoryRepository.find({
          where: { id: In(catalogData.categoryIds) },
        })
      : [];

    const products = catalogData.productIds
      ? await this.productRepository.find({
          where: { id: In(catalogData.productIds) },
        })
      : [];

    const newCatalog = this.catalogRepository.create({
      ...{ name: catalogData.name },
      ...(catalogData.description && { description: catalogData.description }),
      ...(categories.length > 0 && { categories }),
      ...(products.length > 0 && { products }),
    });

    return await this.catalogRepository.save(newCatalog);
  }

  async updateCatalog(catalogId: number, catalogData: UpdateCatalogDto): Promise<Catalog> {
    const catalog = await this.catalogRepository.findOne({
      where: { id: catalogId },
      relations: ['categories', 'products'],
    });
    if (!catalog) {
      throw new Error('Catalog Not Found');
    }
    const categories = catalogData.categoryIds
      ? await this.categoryRepository.find({
          where: { id: In(catalogData.categoryIds) },
        })
      : [];

    const products = catalogData.productIds
      ? await this.productRepository.find({
          where: { id: In(catalogData.productIds) },
        })
      : [];

    catalog.name = catalogData.name ? catalogData.name : catalog.name;
    catalog.description = catalogData.description ? catalogData.description : catalog.description;
    if (categories.length > 0) {
      catalog.categories = categories;
    }
    if (products.length > 0) {
      catalog.products = products;
    }
    return await this.catalogRepository.save(catalog);
  }

  async deleteCatalog(catalogId: number): Promise<Catalog | null> {
    const catalog = await this.catalogRepository.findOne({ where: { id: catalogId } });
    if (!catalog) {
      throw new Error('Catalog not found');
    }
    return await this.catalogRepository.remove(catalog);
  }
}
