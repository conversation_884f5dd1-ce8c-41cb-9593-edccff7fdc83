import { Injectable } from '@nestjs/common';
import { Category } from '../entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { CreateCategoryDto } from '../dto/category/create-category.dto';
import { MediaAsset } from '@api/modules/media-asset/entities/media-asset.entity';
import { UpdateCategoryDto } from '../dto/category/update-category.dto';
@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(MediaAsset)
    private readonly mediaAssetRepository: Repository<MediaAsset>,
  ) {}

  async getCategoryList(
    keyword: string | null = null,
    page: number,
    limit: number,
    parent_category_id: number,
  ): Promise<{ data: Category[]; total: number; page: number; limit: number }> {
    const query = this.categoryRepository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.categoryImage', 'categoryImage')
      .leftJoin('category.parentCategory', 'parentCategory')
      .addSelect(['parentCategory.id', 'parentCategory.name'])
      .orderBy('category.name', 'ASC');

    if (keyword) {
      query.andWhere('category.name LIKE :keyword', { keyword: `%${keyword}%` });
    }
    if (parent_category_id) {
      query.andWhere('category.parentCategory.id = :parent_category_id', { parent_category_id });
    }
    query.skip((page - 1) * limit).take(limit);
    const [data, total] = await query.getManyAndCount();
    return { data, total, page: 1, limit: data.length };
  }

  async getCategoryById(id: number): Promise<Category | null> {
    return await this.categoryRepository.findOne({
      where: { id },
      relations: ['subCategories', 'catalogs', 'products', 'categoryImage'],
    });
  }

  async getCategoryByName(name: string): Promise<Category | null> {
    return await this.categoryRepository.findOne({ where: { name } });
  }

  async createCategory(categoryData: CreateCategoryDto): Promise<Category> {
    let categorymediaAsset = undefined;
    let parentCategory: Category | null = null;
    if (categoryData.categoryImageId) {
      categorymediaAsset = await this.mediaAssetRepository.findOne({
        where: { id: Number(categoryData.categoryImageId) },
      });
      if (categoryData.parentCategoryId) {
        parentCategory = await this.getCategoryById(categoryData.parentCategoryId);
      }
    }
    const newCategory = this.categoryRepository.create({
      name: categoryData.name,
      description: categoryData?.description,
      slug: this.generateSlug(categoryData.name),
      categoryImage: categorymediaAsset ?? undefined,
      parentCategory: parentCategory === null ? undefined : parentCategory,
    });
    return await this.categoryRepository.save(newCategory);
  }

  async updateCategory(id: number, categoryData: UpdateCategoryDto): Promise<Category | null> {
    const category = await this.getCategoryById(id);
    if (!category) {
      throw new Error('Category  not found');
    }

    let categorymediaAsset = undefined;
    let parentCategory: Category | null = null;
    if (categoryData.categoryImageId) {
      categorymediaAsset = await this.mediaAssetRepository.findOne({
        where: { id: Number(categoryData.categoryImageId) },
      });
    }

    if (categoryData.parentCategoryId) {
      parentCategory = await this.categoryRepository.findOne({
        where: { id: Number(categoryData.parentCategoryId) },
      });
    }
    category.name = categoryData.name ? categoryData.name : category.name;
    category.description = categoryData.description ? categoryData.description : category.description;
    category.slug = categoryData.name ? this.generateSlug(categoryData.name) : category.slug;
    if (categorymediaAsset) {
      category.categoryImage = categorymediaAsset;
    }
    if (parentCategory) {
      category.parentCategory = parentCategory;
    }

    return await this.categoryRepository.save(category);
  }

  async deleteCategory(id: number): Promise<Category | null> {
    const category = await this.getCategoryById(id);
    if (!category) {
      throw new Error('Category  not found');
    }
    return await this.categoryRepository.remove(category);
  }

  generateSlug(name: string) {
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w-]+/g, '');
    return slug;
  }
}
