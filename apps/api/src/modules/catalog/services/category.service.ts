import { Injectable } from '@nestjs/common';
import { Category } from '../entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { CreateCategoryDto } from '../dto/category/create-category.dto';
import { MediaAsset } from '@/modules/media-asset/entities/media-asset.entity';
import { UpdateCategoryDto } from '../dto/category/update-category.dto';
@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(MediaAsset)
    private readonly mediaAssetRepository: Repository<MediaAsset>,
  ) {}

  async getCategoryList(keyword: string | null = null): Promise<Partial<Category>[]> {
    let whereCondition = undefined;
    if (keyword) {
      whereCondition = {
        name: Like(`%${keyword}%`),
        description: Like(`%${keyword}%`),
      };
    }

    return await this.categoryRepository.find({ where: whereCondition });
  }

  async getCategoryById(id: number): Promise<Category | null> {
    return await this.categoryRepository.findOne({
      where: { id },
      relations: ['subCategories', 'catalogs', 'products'],
    });
  }

  async getCategoryByName(name: string): Promise<Category | null> {
    return await this.categoryRepository.findOne({ where: { name } });
  }

  async createCategory(categoryData: CreateCategoryDto): Promise<Category> {
    let categorymediaAsset = undefined;
    if (categoryData.categoryImageId) {
      categorymediaAsset = await this.mediaAssetRepository.findOne({
        where: { id: Number(categoryData.categoryImageId) },
      });
    }
    const newCategory = this.categoryRepository.create({
      name: categoryData.name,
      description: categoryData?.description,
      slug: this.generateSlug(categoryData.name),
      categoryImage: categorymediaAsset ?? undefined,
    });
    return await this.categoryRepository.save(newCategory);
  }

  async updateCategory(id: number, categoryData: UpdateCategoryDto): Promise<Category | null> {
    const category = await this.getCategoryById(id);
    if (!category) {
      throw new Error('Category  not found');
    }

    let categorymediaAsset = undefined;
    if (categoryData.categoryImageId) {
      categorymediaAsset = await this.mediaAssetRepository.findOne({
        where: { id: Number(categoryData.categoryImageId) },
      });
    }

    category.name = categoryData.name ? categoryData.name : category.name;
    category.description = categoryData.description ? categoryData.description : category.description;
    category.slug = categoryData.name ? this.generateSlug(categoryData.name) : category.slug;
    if (categorymediaAsset) {
      category.categoryImage = categorymediaAsset;
    }

    return await this.categoryRepository.save(category);
  }

  async deleteCategory(id: number): Promise<Category | null> {
    const category = await this.getCategoryById(id);
    if (!category) {
      throw new Error('Category  not found');
    }
    return await this.categoryRepository.remove(category);
  }

  generateSlug(name: string) {
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w-]+/g, '');
    return slug;
  }
}
