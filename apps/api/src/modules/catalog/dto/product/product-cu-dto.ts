import { IsNotEmpty, <PERSON>N<PERSON><PERSON>, IsString } from 'class-validator';

export class ProductAttributeCUDTO {
  @IsNotEmpty({ message: 'Attribute ID is required' })
  @IsNumber()
  attributeId: number;

  @IsNotEmpty({ message: 'Attribute value is required' })
  @IsString()
  attributeValue: string;

  metadata: Record<string, any>;
}

export class ProductVariationCUDTO {
  @IsString()
  name: string;

  @IsNumber()
  basePrice: number;

  @IsString()
  skucode: string;

  attributes: ProductAttributeCUDTO[];
}

export class ProductCUDTO {
  @IsNotEmpty({ message: 'Name is required' })
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsNotEmpty({ message: 'Slug is required' })
  @IsString()
  slug: string;

  @IsString()
  barcode?: string;

  @IsNotEmpty({ message: 'Product Type ID is required' })
  @IsNumber()
  productTypeId: number;

  productAttributes: ProductAttributeCUDTO[];
  productVariations: ProductVariationCUDTO[];
}
