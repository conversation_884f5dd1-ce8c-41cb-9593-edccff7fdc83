import { IsNotEmpty, IsOptional, IsString, <PERSON><PERSON>ength, IsA<PERSON>y, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateCatalogDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(512)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(2048)
  description?: string;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  categoryIds?: number[];

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  productIds?: number[];
}
