import { Transform } from 'class-transformer';
import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString, MaxLength } from 'class-validator';

export class UpdateCategoryDto {
  @IsNotEmpty({ message: 'Category name is required' })
  @IsString()
  @MaxLength(512, {
    message: 'Category name must be less than or equal to 512 characters',
  })
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(2048, {
    message: 'Category description must be less than or equal to 2048 characters',
  })
  description?: string;

  @IsOptional()
  @IsNumber()
  categoryImageId?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => (value === '' ? undefined : Number(value)))
  parentCategoryId?: number;
}
