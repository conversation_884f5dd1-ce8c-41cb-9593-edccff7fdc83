import { MigrationInterface, QueryRunner } from 'typeorm';
import { DATATYPE_ENUM_TABLE_NAME } from '../entities/datatype-enum.entity';
import { PRODUCT_TYPE_TABLE_NAME } from '../entities/product-type.entity';
import { PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME } from '../entities/product-type-attributes.entity';

export class ProductType_1751808032910 implements MigrationInterface {
  name = 'ProductType_1751808032910';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${DATATYPE_ENUM_TABLE_NAME}\` (
                \`name\` varchar(32) NOT NULL,
                \`description\` varchar(128) NULL,
                PRIMARY KEY (\`name\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${PRODUCT_TYPE_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(32) NOT NULL,
                \`description\` varchar(128) NULL,
                UNIQUE INDEX \`IDX_8978484a9cee7a0c780cd259b8\` (\`name\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`slug\` varchar(64) NOT NULL,
                \`display_name\` varchar(128) NOT NULL,
                \`allow_multiple\` tinyint NOT NULL DEFAULT 0,
                \`is_searchable\` tinyint NOT NULL DEFAULT 0,
                \`is_facet\` tinyint NOT NULL DEFAULT 0,
                \`is_seo_attrib\` tinyint NOT NULL DEFAULT 0,
                \`is_required\` tinyint NOT NULL DEFAULT 0,
                \`is_variant_attribute\` tinyint NOT NULL DEFAULT 0,
                \`is_variant_selector\` tinyint NOT NULL DEFAULT 0,
                \`form_input_type\` varchar(512) NOT NULL,
                \`display_order\` tinyint NOT NULL DEFAULT '1',
                \`form_field_width\` enum ('full', 'half') NOT NULL,
                \`metadata\` json NULL,
                \`product_type_id\` int NOT NULL,
                \`data_type\` varchar(32) NOT NULL,
                UNIQUE INDEX \`IDX_aa79fa82e8286b938060b320b0\` (\`slug\`, \`product_type_id\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_d7450ab6ee1af48a0b29b12dadc\` FOREIGN KEY (\`product_type_id\`) REFERENCES \`${PRODUCT_TYPE_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_5da94b7da7d304bdf0777cbec97\` FOREIGN KEY (\`data_type\`) REFERENCES \`${DATATYPE_ENUM_TABLE_NAME}\`(\`name\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_5da94b7da7d304bdf0777cbec97\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_d7450ab6ee1af48a0b29b12dadc\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_8978484a9cee7a0c780cd259b8\` ON \`${PRODUCT_TYPE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_aa79fa82e8286b938060b320b0\` ON \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PRODUCT_TYPE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${DATATYPE_ENUM_TABLE_NAME}\`
        `);
  }
}
