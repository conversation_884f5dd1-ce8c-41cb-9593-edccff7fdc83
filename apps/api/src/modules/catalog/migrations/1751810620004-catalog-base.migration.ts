import { MigrationInterface, QueryRunner } from 'typeorm';
import { CATALOG_TABLE_NAME } from '../entities/catalog.entity';
import { CATEGORY_TABLE_NAME, CATEGORY_PRODUCTS_TABLE_NAME } from '../entities/category.entity';
import { PRODUCT_TABLE_NAME } from '../entities/product.entity';
import { MEDIA_ASSET_TABLE_NAME } from '@api/modules/media-asset/entities/media-asset.entity';

export class CatalogBase1751810620004 implements MigrationInterface {
  name = 'CatalogBase1751810620004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${CATALOG_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(64) NOT NULL,
                \`description\` varchar(256) NULL,
                UNIQUE INDEX \`IDX_408ad15a08984a8e9b0619ee3e\` (\`name\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${CATEGORY_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(128) NOT NULL,
                \`description\` varchar(256) NULL,
                \`slug\` varchar(64) NOT NULL,
                \`parent_category_id\` int NULL,
                \`catalogs\` json NOT NULL,
                \`category_image_id\` int NULL,
                UNIQUE INDEX \`IDX_cb73208f151aa71cdd78f662d7\` (\`slug\`),
                UNIQUE INDEX \`IDX_f93e6d881c3b8c1c5e7546830d\` (\`name\`, \`parent_category_id\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${CATEGORY_PRODUCTS_TABLE_NAME}\` (
                \`category_id\` int NOT NULL,
                \`product_id\` int NOT NULL,
                INDEX \`IDX_4edf16ad54eaff41c1a9b688c7\` (\`category_id\`),
                INDEX \`IDX_1ae1d4752ec88460e1f65f5c92\` (\`product_id\`),
                PRIMARY KEY (\`category_id\`, \`product_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_0327b1be3eb1a381af34fc4068e\` FOREIGN KEY (\`category_image_id\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_d6db2bf1b938f69d2ebac5a9de8\` FOREIGN KEY (\`parent_category_id\`) REFERENCES \`${CATEGORY_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_PRODUCTS_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_4edf16ad54eaff41c1a9b688c7d\` FOREIGN KEY (\`category_id\`) REFERENCES \`${CATEGORY_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_PRODUCTS_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_1ae1d4752ec88460e1f65f5c929\` FOREIGN KEY (\`product_id\`) REFERENCES \`${PRODUCT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_PRODUCTS_TABLE_NAME}\` DROP FOREIGN KEY \`FK_1ae1d4752ec88460e1f65f5c929\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_PRODUCTS_TABLE_NAME}\` DROP FOREIGN KEY \`FK_4edf16ad54eaff41c1a9b688c7d\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_TABLE_NAME}\` DROP FOREIGN KEY \`FK_d6db2bf1b938f69d2ebac5a9de8\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${CATEGORY_TABLE_NAME}\` DROP FOREIGN KEY \`FK_0327b1be3eb1a381af34fc4068e\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_1ae1d4752ec88460e1f65f5c92\` ON \`${CATEGORY_PRODUCTS_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_4edf16ad54eaff41c1a9b688c7\` ON \`${CATEGORY_PRODUCTS_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${CATEGORY_PRODUCTS_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_408ad15a08984a8e9b0619ee3e\` ON \`${CATALOG_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${CATALOG_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_f93e6d881c3b8c1c5e7546830d\` ON \`${CATEGORY_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_cb73208f151aa71cdd78f662d7\` ON \`${CATEGORY_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${CATEGORY_TABLE_NAME}\`
        `);
  }
}
