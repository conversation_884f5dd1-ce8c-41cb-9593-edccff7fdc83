import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductTypeSchema1746735447700 implements MigrationInterface {
  name = 'ProductTypeSchema1746735447700';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`datatype_enum\` (
                \`name\` varchar(512) NOT NULL,
                \`description\` varchar(2048) NULL,
                PRIMARY KEY (\`name\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`product_type\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(512) NOT NULL,
                \`description\` varchar(2048) NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`product_type_attribute\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`product_type_id\` int NOT NULL,
                \`name\` varchar(512) NOT NULL,
                \`data_type\` varchar(512) NOT NULL,
                \`allow_multiple\` tinyint NOT NULL DEFAULT 0,
                \`is_variant_attribute\` tinyint NOT NULL DEFAULT 0,
                \`is_variant_selector\` tinyint NOT NULL DEFAULT 0,
                \`is_searchable\` tinyint NOT NULL DEFAULT 0,
                \`is_facet\` tinyint NOT NULL DEFAULT 0,
                \`is_seo_attrib\` tinyint NOT NULL DEFAULT 0,
                \`is_required\` tinyint NOT NULL DEFAULT 0,
                \`form_input_type\` varchar(512) NOT NULL,
                \`metadata\` json NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`product_type_attribute\`
            ADD CONSTRAINT \`FK_d7450ab6ee1af48a0b29b12dadc\` FOREIGN KEY (\`product_type_id\`) REFERENCES \`product_type\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`product_type_attribute\`
            ADD CONSTRAINT \`FK_5da94b7da7d304bdf0777cbec97\` FOREIGN KEY (\`data_type\`) REFERENCES \`datatype_enum\`(\`name\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`product_type_attribute\` DROP FOREIGN KEY \`FK_5da94b7da7d304bdf0777cbec97\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`product_type_attribute\` DROP FOREIGN KEY \`FK_d7450ab6ee1af48a0b29b12dadc\`
        `);
    await queryRunner.query(`
            DROP TABLE \`product_type_attribute\`
        `);
    await queryRunner.query(`
            DROP TABLE \`product_type\`
        `);
    await queryRunner.query(`
            DROP TABLE \`datatype_enum\`
        `);
  }
}
