import { ResultSetHeader } from 'mysql2';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class BookProductType1747418512446 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert the Book product type
    const insertResult: ResultSetHeader = await queryRunner.query(`
      INSERT INTO product_type (name, description)
      VALUES ('Book', 'Book')
    `);
    const bookProductTypeId = insertResult.insertId;

    // Insert base product attributes for Book
    await queryRunner.query(`
      INSERT INTO product_type_attribute
      (product_type_id, name, data_type, allow_multiple, is_variant_attribute, is_searchable, is_facet, is_seo_attrib, is_required, form_input_type, metadata)
      VALUES
      (${bookProductTypeId}, 'Language', 'TEXT', 0, 0, 1, 1, 0, 1, 'SINGLE_SELECT_BOX',
        '{"options": [
          {"value": "Bengali", "label": "Bengali"},
          {"value": "English", "label": "English"},
          {"value": "Hindi", "label": "Hindi"},
          {"value": "Marathi", "label": "Marathi"},
          {"value": "Gujarati", "label": "Gujarati"},
          {"value": "Kannada", "label": "Kannada"},
          {"value": "Malayalam", "label": "Malayalam"},
          {"value": "Tamil", "label": "Tamil"},
          {"value": "Telugu", "label": "Telugu"},
          {"value": "Urdu", "label": "Urdu"}
        ]}'
      ),
      (${bookProductTypeId}, 'First Publishing Year', 'TEXT', 0, 0, 0, 0, 0, 0, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Last Publishing Year', 'TEXT', 0, 0, 0, 0, 0, 0, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Book Page Number', 'NUMBER', 0, 0, 1, 1, 0, 1, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Book Meta Title', 'TEXT', 0, 0, 0, 0, 1, 0, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Book Meta Description', 'TEXT', 0, 0, 0, 0, 1, 0, 'TEXT_AREA', NULL),
      (${bookProductTypeId}, 'Book Description', 'TEXT', 0, 0, 0, 0, 0, 0, 'TEXT_EDITOR', NULL),
      (${bookProductTypeId}, 'Book Author', 'AUTHOR', 1, 0, 1, 1, 0, 0, 'AUTHOR_SELECT_BOX', NULL),
      (${bookProductTypeId}, 'Book Publisher', 'PUBLISHER', 0, 0, 1, 1, 0, 1, 'PUBLISHER_SELECT_BOX', NULL),
      (${bookProductTypeId}, 'Book Editor', 'EDITOR', 1, 0, 1, 1, 0, 0, 'EDITOR_SELECT_BOX', NULL),
      (${bookProductTypeId}, 'Book Translator', 'TRANSLATOR', 1, 0, 1, 1, 0, 0, 'TRANSLATOR_SELECT_BOX', NULL),
      (${bookProductTypeId}, 'Book Cover Image', 'MEDIA', 0, 0, 0, 0, 0, 1, 'SINGLE_IMAGE_UPLOAD',
        '{"placeholder": "Recommended Aspect Ratio: 4:6 (Size Should be less than 2MB)"}'
      ),
      (${bookProductTypeId}, 'Book Preview Images', 'MEDIA', 1, 0, 0, 0, 0, 0, 'MULTI_IMAGE_UPLOAD',
        '{"placeholder": "Recommended Aspect Ratio: 4:6 (Size Should be less than 2MB)"}'
      )
    `);

    // Insert variant attributes for Book
    await queryRunner.query(`
      INSERT INTO product_type_attribute
      (product_type_id, name, data_type, allow_multiple,is_variant_selector, is_variant_attribute, is_searchable, is_facet, is_seo_attrib, is_required, form_input_type, metadata)
      VALUES
      (${bookProductTypeId}, 'Edition/Printing Year', 'TEXT', 0, 1, 1, 1, 1, 0, 1, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Binding Type', 'TEXT', 0,1, 1, 1, 1, 0, 1, 'SINGLE_SELECT_BOX',
        '{"options": [
          {"value": "Hardcover", "label": "Hardcover"},
          {"value": "Paperback", "label": "Paperback"},
          {"value": "Spiral", "label": "Spiral"},
          {"value": "Leather", "label": "Leather"},
          {"value": "Board Book", "label": "Board Book"},
          {"value": "Loose Leaf", "label": "Loose Leaf"},
          {"value": "Mass Market Paperback", "label": "Mass Market Paperback"}
        ]}'
      ),
      (${bookProductTypeId}, 'Weight', 'NUMBER', 0,0, 1, 0, 0, 0, 1, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Weight Unit', 'TEXT', 0,0, 1, 0, 0, 0, 1, 'SINGLE_SELECT_BOX',
        '{"options": [
          {"value": "g", "label": "Grams"},
          {"value": "kg", "label": "Kilograms"},
          {"value": "oz", "label": "Ounces"},
          {"value": "lb", "label": "Pounds"}
        ]}'
      ),
      (${bookProductTypeId}, 'Dimensions', 'TEXT', 0,0, 1, 0, 0, 0, 0, 'TEXT_BOX',
        '{"placeholder": "Length x Width x Height (in cm)"}'
      ),
      (${bookProductTypeId}, 'HSN Code', 'TEXT', 0,0, 1, 0, 0, 0, 1, 'TEXT_BOX', NULL),
      (${bookProductTypeId}, 'Book Variant Cover Image', 'MEDIA', 0,0, 1, 0, 0, 0, 0, 'SINGLE_IMAGE_UPLOAD',
        '{"placeholder": "Recommended Aspect Ratio: 4:6 (Size Should be less than 2MB)"}'
      ),
      (${bookProductTypeId}, 'Book Variant Preview Images', 'MEDIA', 1,0, 1, 0, 0, 0, 0, 'MULTI_IMAGE_UPLOAD',
        '{"placeholder": "Recommended Aspect Ratio: 4:6 (Size Should be less than 2MB)"}'
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Get the Book product type ID
    const result = (await queryRunner.query(`
      SELECT id FROM product_type WHERE name = 'Book' LIMIT 1
    `)) as Array<{ id: number }>;

    if (result && result.length > 0) {
      const bookProductTypeId = result[0].id;

      // Delete all product type attributes for Book
      await queryRunner.query(`
        DELETE FROM product_type_attribute WHERE product_type_id = ${bookProductTypeId}
      `);

      // Delete the Book product type
      await queryRunner.query(`
        DELETE FROM product_type WHERE id = ${bookProductTypeId}
      `);
    }
  }
}
