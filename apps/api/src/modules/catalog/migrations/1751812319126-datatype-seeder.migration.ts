import { MigrationInterface, QueryRunner } from 'typeorm';
import { DATATYPE_ENUM_TABLE_NAME } from '../entities/datatype-enum.entity';

export class DatatypeSeeder_1751812319126 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            INSERT INTO \`${DATATYPE_ENUM_TABLE_NAME}\` (name, description) VALUES
            ('TEXT', 'String datatype'),
            ('NUMBER', 'Number datatype'),
            ('BOOLEAN', 'Boolean datatype'),
            ('ISODATE', 'ISO Date string. With timezone'),
            ('WEIGHT', 'Weight will be always stored as gm, and will be formatted during display'),
            ('DIMENSION', 'L x W x H will be always stored as cm, and will be formatted during display'),
            ('MEDIA', 'This datatype refer to entity from Media table.'),
            ('RESOURCE:PUBLISHER', 'This datatype refer to entity from Publisher table.'),
            ('RESOURCE:PEOPLE', 'This datatype refer to entity from People table.')
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DELETE FROM \`${DATATYPE_ENUM_TABLE_NAME}\` WHERE name IN (
            'TEXT', 'NUMBER', 'BOOLEAN', 'ISODATE', 'MEDIA', 'RESOURCE:PUBLISHER', 'RESOURCE:PEOPLE')
        `);
  }
}
