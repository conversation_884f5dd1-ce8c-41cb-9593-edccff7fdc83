import { MigrationInterface, QueryRunner } from 'typeorm';

export class DefaultDatatypes1746812544076 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            INSERT INTO \`datatype_enum\` (name, description) VALUES
            ('TEXT', 'String datatype'),
            ('NUMBER', 'Number datatype'),
            ('BOOLEAN', 'Boolean datatype'),
            ('ISODATE', 'ISO Date string. With timezone'),
            ('MEDIA', 'This datatype refer to entity from Media table.'),
            ('PUBLISHER', 'This datatype refer to entity from Publisher table.'),
            ('AUTHOR', 'This datatype refer to entity from People table.'),
            ('EDITOR', 'This datatype refer to entity from People table.'),
            ('TRANSLATOR', 'This datatype refer to entity from People table.')
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DELETE FROM \`datatype_enum\` WHERE name IN ('TEXT', 'NUMBER', 'BOOLEAN', 'ISODATE', 'MEDIA', 'AUTHOR', 'PUBLISHER', 'EDITOR', 'TRANSLATOR')
        `);
  }
}
