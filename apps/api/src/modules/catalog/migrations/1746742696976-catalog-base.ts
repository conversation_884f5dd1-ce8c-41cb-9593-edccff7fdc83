import { MigrationInterface, QueryRunner } from 'typeorm';

export class CatalogBase1746742696976 implements MigrationInterface {
  name = 'CatalogBase1746742696976';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`product\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(512) NOT NULL,
                \`description\` varchar(2048) NULL,
                \`slug\` varchar(512) NOT NULL,
                \`barcode\` varchar(512) NULL,
                \`product_type_id\` int NOT NULL,
                UNIQUE INDEX \`IDX_8cfaf4a1e80806d58e3dbe6922\` (\`slug\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`category\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(512) NOT NULL,
                \`description\` varchar(2048) NULL,
                \`slug\` varchar(512) NOT NULL,
                \`category_image_id\` int NULL,
                \`parent_category_id\` int NULL,
                UNIQUE INDEX \`IDX_cb73208f151aa71cdd78f662d7\` (\`slug\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`catalog\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(512) NOT NULL,
                \`description\` varchar(2048) NULL,
                UNIQUE INDEX \`IDX_408ad15a08984a8e9b0619ee3e\` (\`name\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`category_has_products\` (
                \`category_id\` int NOT NULL,
                \`product_id\` int NOT NULL,
                INDEX \`IDX_4edf16ad54eaff41c1a9b688c7\` (\`category_id\`),
                INDEX \`IDX_1ae1d4752ec88460e1f65f5c92\` (\`product_id\`),
                PRIMARY KEY (\`category_id\`, \`product_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`category_allowed_product_type\` (
                \`category_id\` int NOT NULL,
                \`product_type_id\` int NOT NULL,
                INDEX \`IDX_8e6618f1759b087846938fa0f1\` (\`category_id\`),
                INDEX \`IDX_176a5a9663e41a4efc7b01dfa5\` (\`product_type_id\`),
                PRIMARY KEY (\`category_id\`, \`product_type_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`catalog_has_categories\` (
                \`catalog_id\` int NOT NULL,
                \`category_id\` int NOT NULL,
                INDEX \`IDX_e8ed3e4174ff9df2c0e123068a\` (\`catalog_id\`),
                INDEX \`IDX_996aa99eb8f711464471d80fd9\` (\`category_id\`),
                PRIMARY KEY (\`catalog_id\`, \`category_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`catalog_has_products\` (
                \`catalog_id\` int NOT NULL,
                \`product_id\` int NOT NULL,
                INDEX \`IDX_8bae1e360ed0727d2596d289bd\` (\`catalog_id\`),
                INDEX \`IDX_3ff7f014ad735f93e3fad7961d\` (\`product_id\`),
                PRIMARY KEY (\`catalog_id\`, \`product_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`product\`
            ADD CONSTRAINT \`FK_9c47355777d0ea7c76aa31059cd\` FOREIGN KEY (\`product_type_id\`) REFERENCES \`product_type\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`category\`
            ADD CONSTRAINT \`FK_d6db2bf1b938f69d2ebac5a9de8\` FOREIGN KEY (\`parent_category_id\`) REFERENCES \`category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`category\`
            ADD CONSTRAINT \`FK_0327b1be3eb1a381af34fc4068e\` FOREIGN KEY (\`category_image_id\`) REFERENCES \`media_assets\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_has_products\`
            ADD CONSTRAINT \`FK_4edf16ad54eaff41c1a9b688c7d\` FOREIGN KEY (\`category_id\`) REFERENCES \`category\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_has_products\`
            ADD CONSTRAINT \`FK_1ae1d4752ec88460e1f65f5c929\` FOREIGN KEY (\`product_id\`) REFERENCES \`product\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_allowed_product_type\`
            ADD CONSTRAINT \`FK_8e6618f1759b087846938fa0f1d\` FOREIGN KEY (\`category_id\`) REFERENCES \`category\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_allowed_product_type\`
            ADD CONSTRAINT \`FK_176a5a9663e41a4efc7b01dfa52\` FOREIGN KEY (\`product_type_id\`) REFERENCES \`product_type\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_categories\`
            ADD CONSTRAINT \`FK_e8ed3e4174ff9df2c0e123068af\` FOREIGN KEY (\`catalog_id\`) REFERENCES \`catalog\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_categories\`
            ADD CONSTRAINT \`FK_996aa99eb8f711464471d80fd92\` FOREIGN KEY (\`category_id\`) REFERENCES \`category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_products\`
            ADD CONSTRAINT \`FK_8bae1e360ed0727d2596d289bd6\` FOREIGN KEY (\`catalog_id\`) REFERENCES \`catalog\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_products\`
            ADD CONSTRAINT \`FK_3ff7f014ad735f93e3fad7961da\` FOREIGN KEY (\`product_id\`) REFERENCES \`product\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_products\` DROP FOREIGN KEY \`FK_3ff7f014ad735f93e3fad7961da\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_products\` DROP FOREIGN KEY \`FK_8bae1e360ed0727d2596d289bd6\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_categories\` DROP FOREIGN KEY \`FK_996aa99eb8f711464471d80fd92\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`catalog_has_categories\` DROP FOREIGN KEY \`FK_e8ed3e4174ff9df2c0e123068af\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_allowed_product_type\` DROP FOREIGN KEY \`FK_176a5a9663e41a4efc7b01dfa52\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_allowed_product_type\` DROP FOREIGN KEY \`FK_8e6618f1759b087846938fa0f1d\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_has_products\` DROP FOREIGN KEY \`FK_1ae1d4752ec88460e1f65f5c929\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`category_has_products\` DROP FOREIGN KEY \`FK_4edf16ad54eaff41c1a9b688c7d\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`category\` DROP FOREIGN KEY \`FK_d6db2bf1b938f69d2ebac5a9de8\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`product\` DROP FOREIGN KEY \`FK_9c47355777d0ea7c76aa31059cd\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_3ff7f014ad735f93e3fad7961d\` ON \`catalog_has_products\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_8bae1e360ed0727d2596d289bd\` ON \`catalog_has_products\`
        `);
    await queryRunner.query(`
            DROP TABLE \`catalog_has_products\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_996aa99eb8f711464471d80fd9\` ON \`catalog_has_categories\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_e8ed3e4174ff9df2c0e123068a\` ON \`catalog_has_categories\`
        `);
    await queryRunner.query(`
            DROP TABLE \`catalog_has_categories\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_176a5a9663e41a4efc7b01dfa5\` ON \`category_allowed_product_type\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_8e6618f1759b087846938fa0f1\` ON \`category_allowed_product_type\`
        `);
    await queryRunner.query(`
            DROP TABLE \`category_allowed_product_type\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_1ae1d4752ec88460e1f65f5c92\` ON \`category_has_products\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_4edf16ad54eaff41c1a9b688c7\` ON \`category_has_products\`
        `);
    await queryRunner.query(`
            DROP TABLE \`category_has_products\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_408ad15a08984a8e9b0619ee3e\` ON \`catalog\`
        `);
    await queryRunner.query(`
            DROP TABLE \`catalog\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_cb73208f151aa71cdd78f662d7\` ON \`category\`
        `);
    await queryRunner.query(`
            DROP TABLE \`category\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_8cfaf4a1e80806d58e3dbe6922\` ON \`product\`
        `);
    await queryRunner.query(`
            DROP TABLE \`product\`
        `);
  }
}
