import { ResultSetHeader } from 'mysql2';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { PRODUCT_TYPE_TABLE_NAME } from '../entities/product-type.entity';
import { PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME } from '../entities/product-type-attributes.entity';

export class BookProductType_1751812549584 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const insertResult: ResultSetHeader = await queryRunner.query(`
      INSERT INTO ${PRODUCT_TYPE_TABLE_NAME} (name, description)
      VALUES ('Book', 'Book product type')
    `);
    const bookProductTypeId = insertResult.insertId;

    // Insert base product attributes for Book
    const languageMetaData = JSON.stringify({
      options: [
        { value: 'Bengali', label: 'Bengali' },
        { value: 'English', label: 'English' },
        { value: 'Hindi', label: 'Hindi' },
        { value: 'Marathi', label: 'Marathi' },
        { value: 'Gujarati', label: 'Gujarati' },
        { value: 'Kannada', label: 'Kannada' },
        { value: 'Malayalam', label: 'Malayalam' },
        { value: 'Tamil', label: 'Tamil' },
        { value: 'Telugu', label: 'Telugu' },
        { value: 'Urdu', label: 'Urdu' },
      ],
    });
    await queryRunner.query(`
      INSERT INTO ${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}(
        product_type_id,slug,display_name,data_type,
        allow_multiple,is_searchable,is_facet,is_seo_attrib,is_required,is_variant_attribute,is_variant_selector,
        form_input_type,display_order,form_field_width, metadata)
      VALUES
      (${bookProductTypeId}, 'language',              'Language',               'TEXT',               0, 1, 1, 0, 1, 0, 0,  'SINGLE_SELECT_BOX',        1, 'half', '${languageMetaData}'),
      (${bookProductTypeId}, 'firstPublishingYear',   'First Publishing Year',  'TEXT',               0, 0, 0, 0, 0, 0, 0,  'TEXT_BOX',                 2, 'half',  NULL),
      (${bookProductTypeId}, 'lastPublishingYear',    'Last Publishing Year',   'TEXT',               0, 0, 0, 0, 0, 0, 0,  'TEXT_BOX',                 3, 'half',  NULL),
      (${bookProductTypeId}, 'metaTitle',             'Book Meta Title',        'TEXT',               0, 0, 0, 1, 0, 0, 0,  'TEXT_BOX',                 4, 'half',  NULL),
      (${bookProductTypeId}, 'metaDescription',       'Book Meta Description',  'TEXT',               0, 0, 0, 1, 0, 0, 0,  'TEXT_AREA',                5, 'half',  NULL),
      (${bookProductTypeId}, 'author',                'Book Author',            'RESOURCE:PEOPLE',    1, 1, 1, 0, 0, 0, 0,  'MULTI_RESOURCE_SELECTOR',  6, 'half',  NULL),
      (${bookProductTypeId}, 'publisher',             'Book Publisher',         'RESOURCE:PUBLISHER', 0, 1, 1, 0, 1, 0, 0,  'SINGLE_RESOURCE_SELECTOR', 7, 'half',  NULL),
      (${bookProductTypeId}, 'editor',                'Book Editor',            'RESOURCE:PEOPLE',    1, 1, 1, 0, 0, 0, 0,  'MULTI_RESOURCE_SELECTOR',  8, 'half',  NULL),
      (${bookProductTypeId}, 'translator',            'Book Translator',        'RESOURCE:PEOPLE',    1, 1, 1, 0, 0, 0, 0,  'MULTI_RESOURCE_SELECTOR',  9,'half',  NULL)
    `);

    // Insert variant attributes for Book
    const bindingTypeMetaData = JSON.stringify({
      options: [
        { value: 'hardCover', label: 'Hardcover' },
        { value: 'paperback', label: 'Paperback' },
        { value: 'spiral', label: 'Spiral' },
        { value: 'leather', label: 'Leather' },
        { value: 'boardBook', label: 'Board Book' },
        { value: 'looseLeaf', label: 'Loose Leaf' },
        { value: 'massMarketPaperback', label: 'Mass Market Paperback' },
      ],
    });
    await queryRunner.query(`
      INSERT INTO ${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME}(
        product_type_id,slug,display_name,data_type,
        allow_multiple,is_searchable,is_facet,is_seo_attrib,is_required,is_variant_attribute,is_variant_selector,
        form_input_type,display_order,form_field_width, metadata)
      VALUES
      (${bookProductTypeId}, 'edition_printing_year', 'Edition/Printing Year',  'TEXT',       0, 1, 1, 0, 1, 1, 1,  'TEXT_BOX',           1, 'half', NULL),
      (${bookProductTypeId}, 'bindingType',           'Binding Type',           'TEXT',       0, 1, 1, 0, 1, 1, 1,  'SINGLE_SELECT_BOX',  2, 'half', '${bindingTypeMetaData}'),
      (${bookProductTypeId}, 'nofOfPages',            'No of pages',            'NUMBER',     0, 0, 0, 0, 1, 1, 0,  'TEXT_BOX',           3, 'half',  NULL),
      (${bookProductTypeId}, 'weight',                'Weight',                 'WEIGHT',     0, 0, 0, 0, 1, 1, 0,  'WEIGHT_SELECTOR',    4, 'half', NULL),
      (${bookProductTypeId}, 'dimension',             'Dimension L x W x H',    'DIMENSION',  0, 0, 0, 0, 0, 1, 0,  'DIMENSION_SELECTOR', 5, 'half', NULL),
      (${bookProductTypeId}, 'hsn_sac_code',          'HSN/SAC Code',           'TEXT',       0, 1, 0, 0, 1, 1, 0,  'TEXT_BOX',           6, 'half', NULL)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const result = (await queryRunner.query(`
      SELECT id FROM ${PRODUCT_TYPE_TABLE_NAME} WHERE name = 'Book' LIMIT 1
    `)) as { id: number }[];

    if (result && result.length > 0) {
      const bookProductTypeId = result[0].id;
      await queryRunner.query(`
        DELETE FROM ${PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME} WHERE product_type_id = ${bookProductTypeId}
      `);
      await queryRunner.query(`
        DELETE FROM ${PRODUCT_TYPE_TABLE_NAME} WHERE id = ${bookProductTypeId}
      `);
    }
  }
}
