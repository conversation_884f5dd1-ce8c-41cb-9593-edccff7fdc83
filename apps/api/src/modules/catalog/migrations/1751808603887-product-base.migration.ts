import { MigrationInterface, QueryRunner } from 'typeorm';
import { PRODUCT_PREVIEW_IMAGE_TABLE_NAME, PRODUCT_TABLE_NAME } from '../entities/product.entity';
import {
  PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME,
  PRODUCT_VARIATION_TABLE_NAME,
} from '../entities/product-variation.entity';
import { MEDIA_ASSET_TABLE_NAME } from '@api/modules/media-asset/entities/media-asset.entity';
import { PRODUCT_TYPE_TABLE_NAME } from '../entities/product-type.entity';

export class ProductBase_1751808603887 implements MigrationInterface {
  name = 'ProductBase_1751808603887';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${PRODUCT_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(512) NOT NULL,
                \`slug\` varchar(128) NOT NULL,
                \`description\` text NULL,
                \`attributes\` json NOT NULL,
                \`catalogs\` json NOT NULL,
                \`product_type_id\` int NOT NULL,
                \`cover_image_id\` int NULL,
                UNIQUE INDEX \`IDX_8cfaf4a1e80806d58e3dbe6922\` (\`slug\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${PRODUCT_VARIATION_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(512) NOT NULL,
                \`barcode\` varchar(64) NULL,
                \`skucode\` varchar(64) NOT NULL,
                \`base_price\` decimal(10, 2) NOT NULL,
                \`is_default_variant\` tinyint NOT NULL DEFAULT '0',
                \`attributes\` json NOT NULL,
                \`cover_image_id\` int NULL,
                \`product_id\` int NULL,
                UNIQUE INDEX \`IDX_82172963517a228e4e9df7436b\` (\`barcode\`),
                UNIQUE INDEX \`IDX_5d017513c954f9dbea06370d47\` (\`skucode\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\` (
                \`variation_id\` int NOT NULL,
                \`media_id\` int NOT NULL,
                INDEX \`IDX_91f4bc9d3f7e55ce18ec24c445\` (\`variation_id\`),
                INDEX \`IDX_96707c5b3e1339ff5c18c4790b\` (\`media_id\`),
                PRIMARY KEY (\`variation_id\`, \`media_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\` (
                \`product_id\` int NOT NULL,
                \`media_id\` int NOT NULL,
                INDEX \`IDX_43e8b47cec7bd3e6a9660b9b05\` (\`product_id\`),
                INDEX \`IDX_30ac1d980eda9472f14f31112e\` (\`media_id\`),
                PRIMARY KEY (\`product_id\`, \`media_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_8f57371e21cb123ed5838309108\` FOREIGN KEY (\`cover_image_id\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_9e6b410563ecd74250629014abe\` FOREIGN KEY (\`product_id\`) REFERENCES \`${PRODUCT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_9c47355777d0ea7c76aa31059cd\` FOREIGN KEY (\`product_type_id\`) REFERENCES \`${PRODUCT_TYPE_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_d6a663860a9272f45f5a0b4ccac\` FOREIGN KEY (\`cover_image_id\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_91f4bc9d3f7e55ce18ec24c445d\` FOREIGN KEY (\`variation_id\`) REFERENCES \`${PRODUCT_VARIATION_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_96707c5b3e1339ff5c18c4790b3\` FOREIGN KEY (\`media_id\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_43e8b47cec7bd3e6a9660b9b051\` FOREIGN KEY (\`product_id\`) REFERENCES \`${PRODUCT_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_30ac1d980eda9472f14f31112ed\` FOREIGN KEY (\`media_id\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_30ac1d980eda9472f14f31112ed\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_43e8b47cec7bd3e6a9660b9b051\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_96707c5b3e1339ff5c18c4790b3\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_91f4bc9d3f7e55ce18ec24c445d\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TABLE_NAME}\` DROP FOREIGN KEY \`FK_d6a663860a9272f45f5a0b4ccac\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_TABLE_NAME}\` DROP FOREIGN KEY \`FK_9c47355777d0ea7c76aa31059cd\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_TABLE_NAME}\` DROP FOREIGN KEY \`FK_9e6b410563ecd74250629014abe\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PRODUCT_VARIATION_TABLE_NAME}\` DROP FOREIGN KEY \`FK_8f57371e21cb123ed5838309108\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_30ac1d980eda9472f14f31112e\` ON \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_43e8b47cec7bd3e6a9660b9b05\` ON \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PRODUCT_PREVIEW_IMAGE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_96707c5b3e1339ff5c18c4790b\` ON \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_91f4bc9d3f7e55ce18ec24c445\` ON \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_8cfaf4a1e80806d58e3dbe6922\` ON \`${PRODUCT_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PRODUCT_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_5d017513c954f9dbea06370d47\` ON \`${PRODUCT_VARIATION_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_82172963517a228e4e9df7436b\` ON \`${PRODUCT_VARIATION_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PRODUCT_VARIATION_TABLE_NAME}\`
        `);
  }
}
