import { Entity, Column, PrimaryGeneratedC<PERSON>umn, ManyToMany, JoinTable } from 'typeorm';
import { Category } from './category.entity';
import { Product } from './product.entity';

export const CATALOG_TABLE_NAME = 'catalog';

@Entity({ name: CATALOG_TABLE_NAME })
export class Catalog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 512, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 2048, nullable: true })
  description: string;

  @ManyToMany(() => Category, (category) => category.catalogs)
  @JoinTable({
    name: 'catalog_has_categories',
    joinColumn: { name: 'catalog_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'category_id', referencedColumnName: 'id' },
  })
  categories: Category[];

  @ManyToMany(() => Product, (product) => product.catalogs)
  @JoinTable({
    name: 'catalog_has_products',
    joinColumn: { name: 'catalog_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'product_id', referencedColumnName: 'id' },
  })
  products: Product[];
}
