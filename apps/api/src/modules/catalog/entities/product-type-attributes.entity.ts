import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, JoinColumn, Unique } from 'typeorm';
import { ProductType } from './product-type.entity';
import { DatatypeEnum } from './datatype-enum.entity';

export const PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME = 'product_type_attribute';

@Entity({ name: PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME })
@Unique(['slug', 'productType'])
export class ProductTypeAttribute {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'product_type_id', type: 'int' })
  productTypeId: number;

  @Column({ type: 'varchar', length: 64 })
  slug: string;

  @Column({ name: 'display_name', type: 'varchar', length: 128 })
  displayName: string;

  @ManyToOne(() => DatatypeEnum, { nullable: false })
  @JoinColumn({ name: 'data_type' })
  dataType: DatatypeEnum;

  @Column({ name: 'allow_multiple', nullable: false, default: false })
  allowMultiple: boolean;

  @Column({ name: 'is_searchable', nullable: false, default: false })
  isSearchable: boolean;

  @Column({ name: 'is_facet', nullable: false, default: false })
  isFacet: boolean;

  @Column({ name: 'is_seo_attrib', nullable: false, default: false })
  isSEOAttrib: boolean;

  @Column({ name: 'is_required', nullable: false, default: false })
  isRequired: boolean;

  @Column({ name: 'is_variant_attribute', nullable: false, default: false })
  isVariantAttribute: boolean;

  @Column({ name: 'is_variant_selector', nullable: false, default: false })
  isVariantSelector: boolean;

  @Column({ name: 'form_input_type', type: 'varchar', length: 512 })
  formInputType: string;

  @Column({ name: 'display_order', type: 'tinyint', default: 1 })
  displayOrder: string;

  @Column({ name: 'form_field_width', type: 'enum', enum: ['full', 'half'] })
  formFieldWidth: 'full' | 'half';

  @Column({ name: 'metadata', type: 'json', nullable: true })
  metadata: Record<string, any>;

  @ManyToOne(() => ProductType, { nullable: false })
  @JoinColumn({ name: 'product_type_id' })
  productType: ProductType;
}
