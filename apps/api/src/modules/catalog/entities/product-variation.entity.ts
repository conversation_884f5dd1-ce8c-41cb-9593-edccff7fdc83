import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, <PERSON>To<PERSON>ne, Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>, ManyToMany } from 'typeorm';
import { Product } from './product.entity';
import { MediaAsset } from '../../media-asset/entities/media-asset.entity';

export const PRODUCT_VARIATION_TABLE_NAME = 'product_variation';
export const PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME = 'product_variation_preview_images';

@Entity({ name: PRODUCT_VARIATION_TABLE_NAME })
export class ProductVariation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 512 })
  name: string;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'cover_image_id' })
  coverImage: MediaAsset;

  @ManyToMany(() => MediaAsset, { nullable: true })
  @JoinTable({
    name: PRODUCT_VARIATION_PREVIEW_IMAGE_TABLE_NAME,
    joinColumn: { name: 'variation_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'media_id', referencedColumnName: 'id' },
  })
  previewImages: MediaAsset[];

  @Column({ type: 'varchar', length: 64, unique: true, nullable: true })
  barcode: string;

  @Column({ type: 'varchar', length: 64, unique: true, nullable: false })
  skucode: string;

  @Column({ name: 'base_price', type: 'decimal', precision: 10, scale: 2 })
  basePrice: number;

  @Column({ name: 'is_default_variant', type: 'boolean', default: 0 })
  isDefaultVariant: boolean;

  /**
   * attribute values
   * key: slug from ProductTypeAttribute
   */
  @Column({ name: 'attributes', type: 'json' })
  attributes: { [key: string]: string };

  @ManyToOne(() => Product, (product) => product.variations)
  @JoinColumn({ name: 'product_id' })
  product: Product;
}
