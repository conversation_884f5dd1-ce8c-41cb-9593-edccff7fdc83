import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, <PERSON>inC<PERSON><PERSON>n, OneToMany } from 'typeorm';
import { Product } from './product.entity';
import { ProductVariationAttribute } from './product-variation-attribute.entity';

export const PRODUCT_VARIATION_TABLE_NAME = 'product_variation';

@Entity({ name: PRODUCT_VARIATION_TABLE_NAME })
export class ProductVariation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  name: string;

  @ManyToOne(() => Product, (product) => product.variations)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ name: 'base_price', type: 'decimal', precision: 10, scale: 2 })
  basePrice: number;

  @Column({ type: 'varchar', length: 1024, unique: true })
  skucode: string;

  @OneToMany(() => ProductVariationAttribute, (productVariationAttribute) => productVariationAttribute.productVariation)
  attributes: ProductVariationAttribute[];
}
