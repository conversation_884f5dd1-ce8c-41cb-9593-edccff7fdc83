import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, OneToMany } from 'typeorm';
import { ProductTypeAttribute } from './product-type-attributes.entity';

export const PRODUCT_TYPE_TABLE_NAME = 'product_type';

@Entity({ name: PRODUCT_TYPE_TABLE_NAME })
export class ProductType {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'name', type: 'varchar', length: 32, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 128, nullable: true })
  description: string;

  @OneToMany(() => ProductTypeAttribute, (pta) => pta.productType)
  attributes: ProductTypeAttribute[];
}
