import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ProductVariation } from './product-variation.entity';
import { ProductTypeAttribute } from './product_type/product-type-attribute.entity';

export const PRODUCT_VARIATION_ATTRIBUTE_TABLE_NAME = 'product_variation_attribute';

@Entity({ name: PRODUCT_VARIATION_ATTRIBUTE_TABLE_NAME })
export class ProductVariationAttribute {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductVariation, (productVariation) => productVariation.attributes)
  @JoinColumn({ name: 'product_variation_id' })
  productVariation: ProductVariation;

  @ManyToOne(() => ProductTypeAttribute)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductTypeAttribute;

  @Column({ name: 'attribute_value', type: 'varchar', length: 4096, nullable: true })
  attributeValue: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;
}
