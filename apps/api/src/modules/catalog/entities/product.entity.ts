import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  JoinTable,
} from 'typeorm';
import { Category } from './category.entity';
import { ProductType } from './product-type.entity';
import { ProductVariation } from './product-variation.entity';
import { MediaAsset } from '../../media-asset/entities/media-asset.entity';

export const PRODUCT_TABLE_NAME = 'product';
export const PRODUCT_PREVIEW_IMAGE_TABLE_NAME = 'product_preview_images';

@Entity({ name: PRODUCT_TABLE_NAME })
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 512 })
  name: string;

  @Column({ type: 'varchar', length: 128, unique: true })
  slug: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'product_type_id', type: 'text', nullable: false })
  productTypeId: string;

  @ManyToOne(() => ProductType, { nullable: false })
  @JoinColumn({ name: 'product_type_id' })
  productType: ProductType;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'cover_image_id' })
  coverImage: MediaAsset;

  @ManyToMany(() => MediaAsset, { nullable: true })
  @JoinTable({
    name: PRODUCT_PREVIEW_IMAGE_TABLE_NAME,
    joinColumn: { name: 'product_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'media_id', referencedColumnName: 'id' },
  })
  previewImages: MediaAsset[];

  /**
   * attribute values
   * key: slug from ProductTypeAttribute
   */
  @Column({ name: 'attributes', type: 'json' })
  attributes: { [key: string]: string };

  @OneToMany(() => ProductVariation, (productVariation) => productVariation.product)
  variations: ProductVariation[];

  /**
   * Which catalogs have this product.
   * key: catalog name
   */
  @Column({ type: 'json' })
  catalogs: { [key: string]: boolean };

  @ManyToMany(() => Category, (category) => category.products)
  @JoinColumn({ name: 'category_id' })
  categories: Category[];
}
