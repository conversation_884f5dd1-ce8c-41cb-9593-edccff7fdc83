import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, JoinColumn, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Category } from './category.entity';
import { Catalog } from './catalog.entity';
import { ProductType } from './product_type/product-type.entity';
import { ProductVariation } from './product-variation.entity';
import { ProductAttribute } from './product-attribute.entity';

export const PRODUCT_TABLE_NAME = 'product';

@Entity({ name: PRODUCT_TABLE_NAME })
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 512 })
  name: string;

  @Column({ type: 'varchar', length: 2048, nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 512, unique: true })
  slug: string;

  @Column({ type: 'varchar', length: 512, nullable: true })
  barcode: string;

  @ManyToOne(() => ProductType, { nullable: false })
  @JoinColumn({ name: 'product_type_id' })
  productType: ProductType;

  @ManyToMany(() => Catalog, (catalog) => catalog.products)
  @JoinColumn({ name: 'catalog_id' })
  catalogs: Catalog[];

  @ManyToMany(() => Category, (category) => category.products)
  @JoinColumn({ name: 'category_id' })
  categories: Category[];

  @OneToMany(() => ProductVariation, (productVariation) => productVariation.product)
  variations: ProductVariation[];

  @OneToMany(() => ProductAttribute, (productAttribute) => productAttribute.product)
  attributes: ProductAttribute[];
}
