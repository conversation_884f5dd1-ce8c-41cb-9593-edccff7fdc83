import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Product } from './product.entity';
import { Catalog } from './catalog.entity';
import { ProductType } from './product_type/product-type.entity';
import { MediaAsset } from '@/modules/media-asset/entities/media-asset.entity';

export const CATEGORY_TABLE_NAME = 'category';

@Entity({ name: CATEGORY_TABLE_NAME })
export class Category {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 512 })
  name: string;

  @Column({ type: 'varchar', length: 2048, nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 512, unique: true })
  slug: string;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'category_image_id' })
  categoryImage: MediaAsset;

  @ManyToOne(() => Category, (category) => category.subCategories, {
    nullable: true,
  })
  @JoinColumn({ name: 'parent_category_id' })
  parentCategory: Category;

  @OneToMany(() => Category, (category) => category.parentCategory, {
    nullable: true,
  })
  subCategories: Category[];

  @ManyToMany(() => Catalog, (catalog) => catalog.categories)
  catalogs: Catalog[];

  @ManyToMany(() => Product, (product) => product.categories)
  @JoinTable({
    name: 'category_has_products',
    joinColumn: { name: 'category_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'product_id', referencedColumnName: 'id' },
  })
  products: Product[];

  @ManyToMany(() => ProductType)
  @JoinTable({
    name: 'category_allowed_product_type',
    joinColumn: { name: 'category_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'product_type_id', referencedColumnName: 'id' },
  })
  allowedProductTypes: ProductType[];
}
