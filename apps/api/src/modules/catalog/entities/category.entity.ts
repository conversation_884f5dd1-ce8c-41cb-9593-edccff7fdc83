import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  ManyToMany,
  JoinTable,
  Unique,
} from 'typeorm';
import { Product } from './product.entity';
import { MediaAsset } from '../../media-asset/entities/media-asset.entity';

export const CATEGORY_TABLE_NAME = 'category';
export const CATEGORY_PRODUCTS_TABLE_NAME = 'category_has_products';

@Entity({ name: CATEGORY_TABLE_NAME })
@Unique(['name', 'parentCategory'])
export class Category {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 128 })
  name: string;

  @Column({ type: 'varchar', length: 256, nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 64, unique: true })
  slug: string;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'category_image_id' })
  categoryImage: MediaAsset;

  @Column({ name: 'parent_category_id', type: 'int', nullable: true })
  parentCategoryId: number;

  @ManyToOne(() => Category, (category) => category.subCategories, { nullable: true })
  @JoinColumn({ name: 'parent_category_id' })
  parentCategory: Category;

  @OneToMany(() => Category, (category) => category.parentCategory, { nullable: true })
  subCategories: Category[];

  /**
   * Which catalogs have this category.
   * key: catalog name
   */
  @Column({ type: 'json' })
  catalogs: { [key: string]: boolean };

  @ManyToMany(() => Product, (product) => product.categories)
  @JoinTable({
    name: CATEGORY_PRODUCTS_TABLE_NAME,
    joinColumn: { name: 'category_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'product_id', referencedColumnName: 'id' },
  })
  products: Product[];
}
