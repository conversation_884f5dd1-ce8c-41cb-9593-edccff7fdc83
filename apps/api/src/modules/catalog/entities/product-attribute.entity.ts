import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, <PERSON>in<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Product } from './product.entity';
import { ProductTypeAttribute } from './product_type/product-type-attribute.entity';

export const PRODUCT_ATTRIBUTE_TABLE_NAME = 'product_attribute';

@Entity({ name: PRODUCT_ATTRIBUTE_TABLE_NAME })
export class ProductAttribute {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Product, (product) => product.attributes)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => ProductTypeAttribute)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductTypeAttribute;

  @Column({ name: 'attribute_value', type: 'varchar', length: 4096, nullable: true })
  attributeValue: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;
}
