import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ProductType } from './product-type.entity';
import { DatatypeEnum } from './datatype-enum.entity';

export const PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME = 'product_type_attribute';

@Entity({ name: PRODUCT_TYPE_ATTRIBUTE_TABLE_NAME })
export class ProductTypeAttribute {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 512 })
  name: string;

  @ManyToOne(() => ProductType, { nullable: false })
  @JoinColumn({ name: 'product_type_id' })
  productType: ProductType;

  @ManyToOne(() => DatatypeEnum, { nullable: false })
  @JoinColumn({ name: 'data_type' })
  dataType: DatatypeEnum;

  /**
   * When this is true that means the product of this type
   * cane have multiple attributes for this attribute_id
   * e.g. a Book can have many Authors
   */
  @Column({ name: 'allow_multiple', nullable: false, default: false })
  allowMultiple: boolean;

  @Column({ name: 'is_variant_attribute', nullable: false, default: false })
  isVariantAttribute: boolean;

  @Column({ name: 'is_variant_selector', nullable: false, default: false })
  isVariantSelector: boolean;

  @Column({ name: 'is_searchable', nullable: false, default: false })
  isSearchable: boolean;

  @Column({ name: 'is_facet', nullable: false, default: false })
  isFacet: boolean;

  @Column({ name: 'is_seo_attrib', nullable: false, default: false })
  isSEOAttrib: boolean;

  @Column({ name: 'is_required', nullable: false, default: false })
  isRequired: boolean;

  @Column({ name: 'form_input_type', type: 'varchar', length: 512 })
  formInputType: string;

  @Column({ name: 'metadata', type: 'json', nullable: true })
  metadata: Record<string, any>;
}
