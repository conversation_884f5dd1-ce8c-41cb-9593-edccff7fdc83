import { Module } from '@nestjs/common';
import { CatalogController } from './controllers/catalog.controller';
import { CategoryController } from './controllers/category.controller';
import { ProductController } from './controllers/product.controller';
import { ProductMediaController } from './controllers/product-media.controller';
import { ProductVariantController } from './controllers/product-variant.controller';
import { ProductVariantMediaController } from './controllers/product-variant-media.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Catalog } from './entities/catalog.entity';
import { Category } from './entities/category.entity';
import { Product } from './entities/product.entity';
import { ProductVariation } from './entities/product-variation.entity';
import { ProductAttribute } from './entities/product-attribute.entity';
import { ProductVariationAttribute } from './entities/product-variation-attribute.entity';
import { ProductType } from './entities/product_type/product-type.entity';
import { ProductTypeAttribute } from './entities/product_type/product-type-attribute.entity';
import { CatalogService } from './services/catalog.service';
import { CategoryService } from './services/category.service';
import { ProductService } from './services/product.service';
import { ProductValidatorService } from './services/product-validator.service';
import { ProductMediaService } from './services/product-media.service';
import { ProductVariantService } from './services/product-variant.service';
import { ProductVariantMediaService } from './services/product-variant-media.service';
import { MediaAsset } from '../media-asset/entities/media-asset.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Catalog,
      Category,
      Product,
      ProductVariation,
      ProductAttribute,
      ProductVariationAttribute,
      ProductType,
      ProductTypeAttribute,
      MediaAsset,
    ]),
  ],
  controllers: [
    CatalogController,
    CategoryController,
    ProductController,
    ProductMediaController,
    ProductVariantController,
    ProductVariantMediaController,
  ],
  providers: [
    CatalogService,
    CategoryService,
    ProductService,
    ProductValidatorService,
    ProductMediaService,
    ProductVariantService,
    ProductVariantMediaService,
  ],
  exports: [
    CatalogService,
    CategoryService,
    ProductService,
    ProductValidatorService,
    ProductMediaService,
    ProductVariantService,
    ProductVariantMediaService,
  ],
})
export class CatalogModule {}
