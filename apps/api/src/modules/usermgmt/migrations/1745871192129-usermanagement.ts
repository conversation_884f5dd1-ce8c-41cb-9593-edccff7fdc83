import { MigrationInterface, QueryRunner } from 'typeorm';

export class Usermanagement_1745871192129 implements MigrationInterface {
  name = 'Usermanagement_1745871192129';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`user\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(1024) NOT NULL,
                \`email_id\` varchar(1024) NOT NULL,
                \`password\` varchar(1024) NOT NULL,
                \`mobile_no\` varchar(16) NULL,
                \`avatar\` int NULL,
                \`is_first_login\` tinyint NOT NULL DEFAULT 1,
                \`password_retry_left\` tinyint NOT NULL DEFAULT '4',
                \`role_id\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`user\`
            ADD UNIQUE INDEX \`IDX_e752aee509d8f8118c6e5b1d8c\` (\`email_id\`)
        `);
    await queryRunner.query(`
            ALTER TABLE \`user\`
            ADD UNIQUE INDEX \`IDX_9ae1452b85736778c53948472b\` (\`mobile_no\`)
        `);
    await queryRunner.query(`
            ALTER TABLE \`user\`
            ADD CONSTRAINT \`FK_36a3fc9cb216b550beee2dce260\` FOREIGN KEY (\`avatar\`) REFERENCES \`media_assets\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`user\`
            ADD CONSTRAINT \`FK_a2cecd1a3531c0b041e29ba46e1\` FOREIGN KEY (\`role_id\`) REFERENCES \`role\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`user\` DROP FOREIGN KEY \`FK_a2cecd1a3531c0b041e29ba46e1\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`user\` DROP FOREIGN KEY \`FK_36a3fc9cb216b550beee2dce260\`
        `);
    await queryRunner.query(`
      ALTER TABLE \`user\` DROP INDEX \`IDX_9ae1452b85736778c53948472b\`
  `);
    await queryRunner.query(`
      ALTER TABLE \`user\` DROP INDEX \`IDX_e752aee509d8f8118c6e5b1d8c\`
  `);
    await queryRunner.query(`
            DROP TABLE \`user\`
        `);
  }
}
