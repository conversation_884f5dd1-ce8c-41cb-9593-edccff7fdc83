import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserOpunitRole1745965218042 implements MigrationInterface {
  name = 'UserOpunitRole1745965218042';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`user_opunit_role\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`user_id\` int NULL,
                \`role_id\` int NULL,
                \`opunit_id\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`user_opunit_role\`
            ADD CONSTRAINT \`FK_d51b553cdcb496f68d2235f44c7\` FOREIGN KEY (\`user_id\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`user_opunit_role\`
            ADD CONSTRAINT \`FK_3ad6355894d0856e33a0803c2e9\` FOREIGN KEY (\`role_id\`) REFERENCES \`role\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`user_opunit_role\`
            ADD CONSTRAINT \`FK_52b5d643a62468aac20d2e28a15\` FOREIGN KEY (\`opunit_id\`) REFERENCES \`opunit\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`user_opunit_role\` DROP FOREIGN KEY \`FK_52b5d643a62468aac20d2e28a15\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`user_opunit_role\` DROP FOREIGN KEY \`FK_3ad6355894d0856e33a0803c2e9\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`user_opunit_role\` DROP FOREIGN KEY \`FK_d51b553cdcb496f68d2235f44c7\`
        `);
    await queryRunner.query(`
            DROP TABLE \`user_opunit_role\`
        `);
  }
}
