import { PERMISSION_TABLE_NAME } from '@api/modules/rbac/entities/permission.entity';
import { ROLE_TABLE_NAME } from '@api/modules/rbac/entities/role.entity';
import * as bcrypt from 'bcrypt';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { USER_TABLE_NAME } from '../entities/user.entity';

export class UserMgmtSeeder_1751803526723 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const adminUserPassword = process.env.ADMIN_USER_DEFAULT_PASSWORD || 'password';
    const hashedPassword = await bcrypt.hash(adminUserPassword, 10);

    const resp = (await queryRunner.query(`SELECT id FROM ${ROLE_TABLE_NAME} WHERE name='Administrator'`)) as [
      { id: number },
    ];
    const adminRoleId = resp[0].id;

    await queryRunner.query(`
          INSERT INTO ${USER_TABLE_NAME} (name, email_id, password, mobile_no, avatar, role_id, is_first_login, password_retry_left)
          VALUES ('Super Admin', '<EMAIL>', '${hashedPassword}', '1234567890', NULL, ${adminRoleId}, true, 4)
        `);

    await queryRunner.query(
      `INSERT INTO ${PERMISSION_TABLE_NAME} (slug, name, description, module, \`groups\`, level) VALUES
               ('MANAGE_USER', 'Manage User', 'Can create, update, view, and delete any user.', 'usermgmt', 'user','all'),
               ('VIEW_USERS', 'View Users', 'Can view available users.', 'usermgmt', 'user','all'),
               ('MANAGE_USER_OP_UNIT', 'Mange User Operational Unit', 'Can create, update, view, and delete any user opunit.', 'usermgmt', 'opunit','org');`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('MANAGE_USER', 'VIEW_USERS', 'MANAGE_USER_OP_UNIT');
    `);

    await queryRunner.query(`
      DELETE FROM ${USER_TABLE_NAME} WHERE email_id = '<EMAIL>'
    `);
  }
}
