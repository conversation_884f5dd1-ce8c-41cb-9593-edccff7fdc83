import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { ROLE_TABLE_NAME } from '@/modules/rbac/entities/role.entity';

export class CreateAdminUser1745914948724 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const adminUserPassword = process.env.ADMIN_USER_DEFAULT_PASSWORD || 'password';
    const hashedPassword = await bcrypt.hash(adminUserPassword, 10);

    const resp = (await queryRunner.query(`SELECT id FROM ${ROLE_TABLE_NAME} WHERE name='Administrator'`)) as [
      { id: number },
    ];
    const adminRoleId = resp[0].id;

    await queryRunner.query(`
      INSERT INTO user (name, email_id, password, mobile_no, avatar, role_id, is_first_login, password_retry_left)
      VALUES ('Super Admin', '<EMAIL>', '${hashedPassword}', '1234567890', NULL, ${adminRoleId}, true, 4)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM user WHERE email_id = '<EMAIL>'
    `);
  }
}
