import { MigrationInterface, QueryRunner } from 'typeorm';
import { USER_TABLE_NAME } from '../entities/user.entity';
import { USER_OPUNIT_ROLE_TABLE_NAME } from '../entities/useropunitrole.entity';
import { MEDIA_ASSET_TABLE_NAME } from '@api/modules/media-asset/entities/media-asset.entity';
import { ROLE_TABLE_NAME } from '@api/modules/rbac/entities/role.entity';
import { OPUNIT_TABLE_NAME } from '@api/modules/opunitmgmt/entities/operationalunit.entity';

export class UserManagementBase_1751803374791 implements MigrationInterface {
  name = 'UserManagementBase_1751803374791';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${USER_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(128) NOT NULL,
                \`email_id\` varchar(128) NOT NULL,
                \`password\` varchar(256) NOT NULL,
                \`mobile_no\` varchar(16) NULL,
                \`is_first_login\` tinyint NOT NULL DEFAULT 1,
                \`password_retry_left\` smallint NOT NULL DEFAULT '4',
                \`avatar\` int NULL,
                \`role_id\` int NULL,
                UNIQUE INDEX \`IDX_8f8d0ce13a8930a39ebc2cbb26\` (\`email_id\`),
                UNIQUE INDEX \`IDX_ca16ee8c3305d46c903a92d36f\` (\`mobile_no\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`user_id\` int NULL,
                \`role_id\` int NULL,
                \`opunit_id\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_6d643a2e8ae462e7b41024fa975\` FOREIGN KEY (\`avatar\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_00b3d05ef2cf349712d4bd2513f\` FOREIGN KEY (\`role_id\`) REFERENCES \`${ROLE_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_d51b553cdcb496f68d2235f44c7\` FOREIGN KEY (\`user_id\`) REFERENCES \`${USER_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_3ad6355894d0856e33a0803c2e9\` FOREIGN KEY (\`role_id\`) REFERENCES \`${ROLE_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_52b5d643a62468aac20d2e28a15\` FOREIGN KEY (\`opunit_id\`) REFERENCES \`${OPUNIT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_52b5d643a62468aac20d2e28a15\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_3ad6355894d0856e33a0803c2e9\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_d51b553cdcb496f68d2235f44c7\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_TABLE_NAME}\` DROP FOREIGN KEY \`FK_00b3d05ef2cf349712d4bd2513f\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${USER_TABLE_NAME}\` DROP FOREIGN KEY \`FK_6d643a2e8ae462e7b41024fa975\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${USER_OPUNIT_ROLE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_ca16ee8c3305d46c903a92d36f\` ON \`${USER_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_8f8d0ce13a8930a39ebc2cbb26\` ON \`${USER_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${USER_TABLE_NAME}\`
        `);
  }
}
