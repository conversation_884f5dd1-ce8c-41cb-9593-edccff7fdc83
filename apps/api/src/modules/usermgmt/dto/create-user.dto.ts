import { Type } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString, Matches, ValidateIf, IsNumber, IsOptional } from 'class-validator';
import { Match } from '@api/decorators/match.decorator';

export class CreateUserDto {
  @IsNotEmpty({ message: 'Name is required' })
  @IsString()
  name: string;

  @IsNotEmpty({ message: 'Password is required' })
  @IsString()
  password: string;

  @IsNotEmpty({ message: 'Password confirmation is required' })
  @IsString()
  @Match('password', { message: 'Password confirmation must match password' })
  password_confirmation: string;

  @ValidateIf((o: CreateUserDto) => !o.mobileNo)
  @IsNotEmpty({ message: 'Email is required if mobile is not provided' })
  @IsEmail({}, { message: 'Invalid email format' })
  emailId: string;

  @ValidateIf((o: CreateUserDto) => !o.emailId)
  @IsNotEmpty({ message: 'Mobile is required if email is not provided' })
  @Matches(/^\+\d{8,20}$/, {
    message: 'Mobile number must be in international format (e.g., +1234567890)',
  })
  mobileNo: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Role must be a valid number' })
  role: number | null;
}
