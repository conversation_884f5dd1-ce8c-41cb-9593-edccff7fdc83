import { Type } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString, Matches, ValidateIf, IsNumber, IsOptional } from 'class-validator';
import { Match } from 'src/decorators/match.decorator';

export class UpdateUserDto {
  @IsNotEmpty({ message: 'Name is required' })
  @IsString()
  name: string;

  @IsString()
  password: string;

  @ValidateIf((o: UpdateUserDto) => !!o.password)
  @IsNotEmpty({ message: 'Password confirmation is required' })
  @IsString()
  @Match('password', { message: 'Password confirmation must match password' })
  password_confirmation: string;

  @ValidateIf((o: UpdateUserDto) => !o.mobileNo)
  @IsNotEmpty({ message: 'Email is required if mobile is not provided' })
  @IsEmail({}, { message: 'Invalid email format' })
  emailId: string;

  @ValidateIf((o: UpdateUserDto) => !o.emailId)
  @IsNotEmpty({ message: 'Mobile is required if email is not provided' })
  @Matches(/^\+\d{8,20}$/, {
    message: 'Mobile number must be in international format (e.g., +1234567890)',
  })
  mobileNo: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Role must be a valid number' })
  role: number | null;
}
