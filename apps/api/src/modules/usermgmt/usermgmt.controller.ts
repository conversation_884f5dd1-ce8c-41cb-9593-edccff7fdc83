import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiCookieAuth, ApiTags } from '@nestjs/swagger';
import { UsermgmtService } from './usermgmt.service';
import { UserListDto } from './dto/user-list.dto';
import { ApiResponseType } from '@common-utils/api-types';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AddOpUnitDto } from './dto/add-opunit.dto';
import { UserOpunitRole } from './entities/useropunitrole.entity';
import { AccessGuard } from '../auth/guards/access.guard';
import { Permissions } from '../auth/decorators/permission.decorator';
import { ListUserSwaggerDoc } from './api-docs/list-user';
import { ViewUserSwaggerDoc } from './api-docs/show-user';
import { CreateUserSwaggerDoc } from './api-docs/create-user';
import { UpdateUserSwaggerDoc } from './api-docs/update-user';
import { UserAddOpUnitSwaggerDoc } from './api-docs/user-add-opunit';
import { UserRemoveOpunitSwaggerDoc } from './api-docs/user-remove-opunit';
import { UserGetOpunitSwaggerDoc } from './api-docs/user-get-opunits';

@ApiTags('User Management')
@Controller('users')
export class UsermgmtController {
  constructor(private readonly usermgmtService: UsermgmtService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_USERS')
  @ApiCookieAuth()
  @ListUserSwaggerDoc()
  @Get()
  async list(@Query() query: UserListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.usermgmtService.getUserList(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Data retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_USERS')
  @ApiCookieAuth()
  @ViewUserSwaggerDoc()
  @Get('/:userId')
  async detail(@Param('userId') userId: number): Promise<ApiResponseType<any>> {
    const user = await this.usermgmtService.getUserById(userId);
    if (!user) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Data retrieved successfully',
      data: user,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_USER')
  @ApiCookieAuth()
  @CreateUserSwaggerDoc()
  @Post()
  async create(@Body() data: CreateUserDto): Promise<ApiResponseType<any>> {
    //check the email already exists or not is we have email in data
    const userEmail = await this.usermgmtService.getUserByEmail(data.emailId);
    if (userEmail) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Email already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    //Check the mobile number already exists or not if we have mobile number in data
    const userMobile = await this.usermgmtService.getUserByMobile(data.mobileNo);
    if (userMobile) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Mobile already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    //Create the user
    const user = await this.usermgmtService.createUser(data);
    return {
      status: 'success',
      message: 'User created successfully',
      data: user,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_USER')
  @ApiCookieAuth()
  @UpdateUserSwaggerDoc()
  @Put('/:userId')
  async update(@Param('userId') userId: number, @Body() data: UpdateUserDto): Promise<ApiResponseType<any>> {
    //Check if the user exists
    const checkUser = await this.usermgmtService.getUserById(userId);
    if (!checkUser) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No user found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    //Validate email
    if (data.emailId) {
      const userEmail = await this.usermgmtService.getUserByEmail(data.emailId);
      if (userEmail && userEmail.id !== userId) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Email already exists',
          },
          HttpStatus.CONFLICT,
        );
      }
    }
    //Validate mobile number
    if (data.mobileNo) {
      const userMobile = await this.usermgmtService.getUserByMobile(data.mobileNo);
      if (userMobile && userMobile.id !== userId) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Mobile already exists',
          },
          HttpStatus.CONFLICT,
        );
      }
    }
    //Update the user
    const user = await this.usermgmtService.updateUser(userId, data);
    return {
      status: 'success',
      message: 'User updated successfully',
      data: user,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_USER_OP_UNIT')
  @ApiCookieAuth()
  @UserAddOpUnitSwaggerDoc()
  @Post('add-opunit')
  async addOpunit(@Body() data: AddOpUnitDto): Promise<ApiResponseType<UserOpunitRole>> {
    const opunit = await this.usermgmtService.addOpunit(data);
    return {
      status: 'success',
      message: 'User added to operation unit successfully',
      data: opunit,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_USER_OP_UNIT')
  @ApiCookieAuth()
  @UserRemoveOpunitSwaggerDoc()
  @Delete('remove-opunit/:userOpunitId')
  async removeOpunit(@Param('userOpunitId') userOpunitId: number): Promise<ApiResponseType<any>> {
    // Validate if the operation unit exists
    const opunit = await this.usermgmtService.getOpunitById(userOpunitId);
    if (!opunit) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Operational Unit not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.usermgmtService.removeOpunit(userOpunitId);
    return {
      status: 'success',
      message: 'Operational Unit deleted successfully',
      data: null,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_USERS')
  @ApiCookieAuth()
  @UserGetOpunitSwaggerDoc()
  @Get('/:userId/get-opunits')
  async getOpunits(@Param('userId') userId: number): Promise<ApiResponseType<any>> {
    const opunits = await this.usermgmtService.getOpunitsByUserId(userId);
    if (!opunits) {
      throw new HttpException(
        {
          status: 'error',
          message: 'User not found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Operation units retrieved successfully',
      data: opunits,
    };
  }
}
