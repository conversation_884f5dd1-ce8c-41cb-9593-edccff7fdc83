import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';

export function UpdateUserSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Update user' }),
    ApiBody({
      description: 'Payload to update a user',
      schema: {
        type: 'object',
        required: ['name', 'emailId', 'mobileNo'],
        properties: {
          name: {
            type: 'string',
            example: '<PERSON>',
            description: 'Name of the person',
          },
          emailId: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email address of the user',
          },
          mobileNo: {
            type: 'string',
            example: '+1234567890',
            description: 'Mobile number of the user',
          },
          password: {
            type: 'string',
            example: 'password',
            description: 'Password of the user',
          },
          password_confirmation: {
            type: 'string',
            example: 'password',
            description: 'Password confirmation of the user',
          },
          role: {
            type: 'number',
            example: 1,
            description: 'Role of the user',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'User updated successfully',
      schema: {
        example: {
          status: 'success',
          message: 'User updated successfully',
          data: {
            id: 2,
            name: 'Demo Name',
            emailId: '<EMAIL>',
            password: 'password',
            mobileNo: '9089009090',
            avatar: null,
            role: {
              id: 1,
              name: 'Administrator',
              description: 'Administrator',
              isReadOnly: true,
              rolePosition: 10,
            },
            isFirstLogin: true,
            passwordRetryLeft: 4,
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'User not found',
      schema: {
        example: {
          status: 'error',
          message: 'User not found',
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Validation failed for the input data',
      schema: {
        example: {
          statusCode: 400,
          error: 'Bad Request',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict: Email/Mobile already exists',
      schema: {
        example: {
          status: 'error',
          message: 'Email/Mobile already exists',
        },
      },
    }),
  );
}
