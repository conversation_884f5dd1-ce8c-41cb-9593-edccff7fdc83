import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function ViewUserSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Get user by id' }),
    ApiResponse({
      status: 200,
      description: 'User retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'User retrieved successfully',
          data: {
            id: 1,
            name: 'Super Admin',
            emailId: '<EMAIL>',
            password: '$2b$10$qodku3G0b6m7f8cc8IDfCu0bs.L7bQBtIHfnyYCT4KG4hT5l.x87q',
            mobileNo: '1234567890',
            avatar: null,
            isFirstLogin: true,
            passwordRetryLeft: 4,
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'No data found',
      schema: {
        example: {
          status: 'error',
          message: 'No Data found',
          data: [],
        },
      },
    }),
  );
}
