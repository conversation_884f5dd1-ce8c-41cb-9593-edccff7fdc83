import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function UserGetOpunitSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Get all operation units of a user' }),
    ApiResponse({
      status: 200,
      description: 'Operation units retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Operation units retrieved successfully',
          data: [
            {
              id: 1,
              role: {
                id: 1,
                name: 'Administrator',
                description: 'Administrator',
                isReadOnly: true,
                rolePosition: 10,
              },
              opunit: {
                id: 1,
                name: 'Main Warehouse',
                address: 'Kolkata',
                emailId: '<EMAIL>',
                mobileNo: '+919062916989',
                isActive: 1,
                isInfiniteStock: 0,
              },
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'User not found',
      schema: {
        example: {
          status: 'error',
          message: 'User not found',
          data: [],
        },
      },
    }),
  );
}
