import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';

export function ListUserSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Get list of users' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to filter data',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page for pagination',
      example: 10,
    }),
    ApiResponse({
      status: 404,
      description: 'No data found',
      schema: {
        example: {
          status: 'error',
          message: 'No data found',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Data retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Data retrieved successfully',
          data: {
            items: [
              {
                id: '<number>',
                name: '<string>',
                emailId: '<string>',
                password: '<string>',
                mobileNo: '<string>',
                avatar: null,
                isFirstLogin: '<boolean>',
                passwordRetryLeft: '<number>',
              },
            ],
            total: '<number>',
            page: '<number>',
            limit: '<number>',
          },
        },
      },
    }),
  );
}
