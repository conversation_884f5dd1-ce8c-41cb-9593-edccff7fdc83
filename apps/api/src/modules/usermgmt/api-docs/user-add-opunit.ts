import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';

export function UserAddOpUnitSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Add user to an operation unit' }),
    ApiBody({
      description: 'Payload to add user to an operation unit',
      schema: {
        type: 'object',
        required: ['userId', 'opunitId'],
        properties: {
          opunitId: {
            type: 'number',
            example: 1,
            description: 'ID of the operation unit',
          },
          userId: {
            type: 'number',
            example: 1,
            description: 'ID of the user to be added',
          },
          roleId: {
            type: 'number',
            example: 1,
            description: 'ID of the role to be assigned',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'User added to operation unit successfully',
      schema: {
        example: {
          status: 'success',
          message: 'User added to operation unit successfully',
          data: {
            id: 1,
            user: {
              id: 1,
              name: 'Super Admin',
              emailId: '<EMAIL>',
              password: 'demo-pass',
              mobileNo: '1234567890',
              avatar: null,
              isFirstLogin: true,
              passwordRetryLeft: 4,
            },
            role: {
              id: 1,
              name: 'Administrator',
              description: 'Administrator',
              isReadOnly: true,
              rolePosition: 10,
            },
            opunit: {
              id: 1,
              name: 'Main Warehouse',
              address: 'Kolkata',
              emailId: '<EMAIL>',
              mobileNo: '+919062916989',
              isActive: 1,
              isInfiniteStock: 0,
            },
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'User not found',
      schema: {
        example: {
          status: 'error',
          message: 'User not found',
          data: [],
        },
      },
    }),
  );
}
