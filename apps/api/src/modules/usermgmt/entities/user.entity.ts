import { Role } from '@/modules/rbac/entities/role.entity';
import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { UserOpunitRole } from './useropunitrole.entity';
import { MediaAsset } from '@/modules/media-asset/entities/media-asset.entity';

export const USER_TABLE_NAME = 'user';

@Entity({ name: USER_TABLE_NAME })
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 1024 })
  name: string;

  @Column({
    name: 'email_id',
    type: 'varchar',
    length: 1024,
    unique: true,
  })
  emailId: string;

  @Column({ type: 'varchar', length: 1024 })
  password: string;

  @Column({ name: 'mobile_no', type: 'varchar', length: 16, nullable: true, unique: true })
  mobileNo: string;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'avatar' })
  avatar: MediaAsset;

  /**
   * This role is user's org level assigned role
   * This can be null. Means user don't have any org level role
   */
  @ManyToOne(() => Role, (role) => role, { nullable: true })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ name: 'is_first_login', type: 'boolean', default: true })
  isFirstLogin: true;

  @Column({ name: 'password_retry_left', type: 'tinyint', default: 4 })
  passwordRetryLeft: true;

  @OneToMany(() => UserOpunitRole, (udr) => udr.user)
  opunitRoles: UserOpunitRole[];
}
