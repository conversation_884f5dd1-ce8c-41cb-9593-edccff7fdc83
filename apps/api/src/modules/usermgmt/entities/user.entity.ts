import { Role } from '@api/modules/rbac/entities/role.entity';
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { UserOpunitRole } from './useropunitrole.entity';
import { MediaAsset } from '@api/modules/media-asset/entities/media-asset.entity';

export const USER_TABLE_NAME = 'app_user';

@Entity({ name: USER_TABLE_NAME })
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 128 })
  name: string;

  @Column({ name: 'email_id', type: 'varchar', length: 128, unique: true })
  emailId: string;

  @Column({ type: 'varchar', length: 256 })
  password: string;

  @Column({ name: 'mobile_no', type: 'varchar', length: 16, nullable: true, unique: true })
  mobileNo: string;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'avatar' })
  avatar: MediaAsset;

  /**
   * This role is user's org level assigned role
   * This can be null. Means user don't have any org level role
   */
  @ManyToOne(() => Role, (role) => role, { nullable: true })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ name: 'is_first_login', type: 'boolean', default: true })
  isFirstLogin: true;

  @Column({ name: 'password_retry_left', type: 'smallint', default: 4 })
  passwordRetryLeft: true;

  @OneToMany(() => UserOpunitRole, (udr) => udr.user)
  opunitRoles: UserOpunitRole[];
}
