import { Role } from '@/modules/rbac/entities/role.entity';
import { <PERSON>tity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { Opunit } from '@/modules/opunitmgmt/entities/operationalunit.entity';

export const USER_OPUNIT_ROLE_TABLE_NAME = 'user_opunit_role';

@Entity({ name: USER_OPUNIT_ROLE_TABLE_NAME })
export class UserOpunitRole {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, (user) => user.opunitRoles)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @ManyToOne(() => Opunit)
  @JoinColumn({ name: 'opunit_id' })
  opunit: Opunit;
}
