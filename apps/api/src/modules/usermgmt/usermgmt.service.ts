import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOneOptions, Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { Role } from '../rbac/entities/role.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import * as bcrypt from 'bcrypt';
import { AddOpUnitDto } from './dto/add-opunit.dto';
import { UserOpunitRole } from './entities/useropunitrole.entity';
import { Opunit } from '../opunitmgmt/entities/operationalunit.entity';
import { Permission } from '../rbac/entities/permission.entity';

@Injectable()
export class UsermgmtService {
  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Role) private roleRepository: Repository<Role>,
    @InjectRepository(UserOpunitRole)
    private userOpunitRoleRepository: Repository<UserOpunitRole>,
    @InjectRepository(Opunit)
    private readonly opunitRepository: Repository<Opunit>,
  ) {}

  async getUserList(
    keyword: string | null = null,
    page: number,
    limit: number,
  ): Promise<{
    data: User[];
    total: number;
    page: number;
    limit: number;
  }> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.role', 'role')
      .select(['user.id', 'user.name', 'user.emailId', 'user.mobileNo', 'user.avatar', 'role.id', 'role.name']);

    if (keyword) {
      queryBuilder.where('user.name LIKE :keyword', {
        keyword: `%${keyword}%`,
      });
    }

    queryBuilder
      .orderBy('user.name', 'ASC')
      .skip((page - 1) * limit)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total, page, limit };
  }

  async getUserById(userId: number): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { emailId: email },
    });
  }

  async getUserByMobile(mobile: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { mobileNo: mobile },
    });
  }

  async createUser(data: CreateUserDto): Promise<User> {
    //Check Role is exists or not
    let role: Role | null = null;
    if (data.role) {
      role = await this.roleRepository.findOne({
        where: { id: data.role },
      });
    }
    // Hash the password
    const hashedPassword = await bcrypt.hash(data.password, 10);
    const user = this.userRepository.create({
      ...data,
      password: hashedPassword,
      role: role === null ? undefined : role,
    });
    return await this.userRepository.save(user);
  }

  async updateUser(userId: number, data: UpdateUserDto): Promise<User> {
    //Check Role is exists or not
    let role: Role | null = null;
    if (data.role) {
      role = await this.roleRepository.findOne({
        where: { id: data.role },
      });
    }
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      throw new Error('User not found');
    }
    user.name = data.name;
    user.emailId = data.emailId ?? null;
    user.mobileNo = data.mobileNo ?? null;
    if (data.password) {
      user.password = await bcrypt.hash(data.password, 10);
    }
    if (role) {
      user.role = role;
    }
    return await this.userRepository.save(user);
  }

  async getUser(query: FindOneOptions<User>): Promise<User | null> {
    return await this.userRepository.findOne({
      ...query,
      relations: ['role'],
    });
  }

  async addOpunit(data: AddOpUnitDto): Promise<UserOpunitRole> {
    const user = await this.userRepository.findOne({
      where: { id: Number(data.userId) },
    });
    if (!user) {
      throw new Error('User not found');
    }
    const role = await this.roleRepository.findOne({
      where: { id: Number(data.roleId) },
    });
    if (!role) {
      throw new Error('Role not found');
    }
    const opunit = await this.opunitRepository.findOne({
      where: { id: Number(data.opunitId) },
    });
    if (!opunit) {
      throw new Error('Opunit not found');
    }
    const userOpunitRole = this.userOpunitRoleRepository.create({
      user: user,
      role: role,
      opunit: opunit,
    });
    return await this.userOpunitRoleRepository.save(userOpunitRole);
  }

  async getOpunitById(userOpunitId: number): Promise<UserOpunitRole | null> {
    return await this.userOpunitRoleRepository.findOne({
      where: { id: userOpunitId },
    });
  }

  async removeOpunit(userOpunitId: number): Promise<void> {
    const userOpunitRole = await this.userOpunitRoleRepository.findOne({
      where: { id: userOpunitId },
    });
    if (!userOpunitRole) {
      throw new Error('UserOpunitRole not found');
    }
    await this.userOpunitRoleRepository.remove(userOpunitRole);
  }

  async getAllOpunits(): Promise<Opunit[]> {
    return await this.opunitRepository.find();
  }

  async getOpunitsByUserId(userId: number): Promise<UserOpunitRole[]> {
    return await this.userOpunitRoleRepository.find({
      where: { user: { id: userId } },
      relations: ['opunit', 'role'],
    });
  }

  async getPlatformLevelPermissionsByUser(userId: number): Promise<string[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });
    if (!user || !user.role) {
      return [];
    }
    // Extract and return the permission slugs
    const permissions = user.role.permissions.map((permission) => permission.slug);
    return permissions;
  }

  async checkAccessToResource(userId: number, resources: string[]): Promise<boolean> {
    if (!resources || resources.length === 0) {
      return false; // No resources to check access for
    }

    const query = `
      SELECT COUNT(*) AS count
      FROM role_has_permissions rp
      INNER JOIN permission p ON rp.permissionId = p.id
      INNER JOIN role r ON rp.roleId = r.id
      INNER JOIN users u ON u.roleId = r.id
      WHERE u.id = ? AND p.slug IN (${resources.map(() => '?').join(', ')})
    `;
    const parameters: any[] = [userId, ...resources];
    type QueryResult = { count: string }[];
    try {
      const result: QueryResult = await this.userRepository.query(query, parameters);
      const count = parseInt(result[0]?.count || '0', 10);
      return count > 0;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async checkPermissionToOpunitResource(userId: number, opunitId: number, resources: string[]): Promise<boolean> {
    if (!resources || resources.length === 0) {
      return false; // No resources to check access for
    }
    const query = `
      SELECT COUNT(*) AS count
      FROM user_opunit_role upr
      INNER JOIN role r ON upr.roleId = r.id
      INNER JOIN role_has_permissions rhp ON r.id = rhp.roleId
      INNER JOIN permission p ON rhp.permissionId = p.id
      WHERE upr.userId = ? AND upr.opunitId = ? AND p.slug IN (${resources.map(() => '?').join(', ')})
    `;
    const parameters: any[] = [userId, opunitId, ...resources];
    type QueryResult = { count: string }[];
    try {
      const result: QueryResult = await this.userRepository.manager.query(query, parameters);
      const count = parseInt(String(result[0]?.count || '0'), 10);
      return count > 0;
    } catch (error) {
      console.error('Error checking permission to opunit resource:', error);
      return false;
    }
  }

  async getUsersOpunitAccess(userId: number): Promise<any[]> {
    const userOpunitRoles = await this.userOpunitRoleRepository.find({
      where: { user: { id: userId } },
      relations: ['opunit'],
    });
    const uniqueOpunits = Array.from(new Map(userOpunitRoles.map((role) => [role.opunit.id, role.opunit])).values());
    return uniqueOpunits;
  }

  async validateOpunit(id: number): Promise<Opunit | null> {
    return await this.opunitRepository.findOne({
      where: { id },
    });
  }

  async getOpunitAccess(userId: number, opunitId: number): Promise<boolean> {
    const userOpunitRole = await this.userOpunitRoleRepository.findOne({
      where: {
        user: { id: userId },
        opunit: { id: opunitId },
      },
    });
    return !!userOpunitRole;
  }

  async getAccessByOpunit(userId: number, opunitId: number): Promise<Permission[]> {
    const userOpunitRole = await this.userOpunitRoleRepository.findOne({
      where: {
        user: { id: userId },
        opunit: { id: opunitId },
      },
      relations: ['role', 'role.permissions'],
    });

    if (!userOpunitRole) {
      throw new Error('UserOpunitRole not found');
    }

    // Extract permissions and ensure uniqueness
    const uniquePermissions = Array.from(
      new Map(userOpunitRole.role.permissions.map((permission) => [permission.id, permission])).values(),
    );

    return uniquePermissions;
  }

  async getAccessByOpunitModule(userId: number, opunitId: number, module: string): Promise<Permission[]> {
    const userOpunitRole = await this.userOpunitRoleRepository.findOne({
      where: {
        user: { id: userId },
        opunit: { id: opunitId },
      },
      relations: ['role', 'role.permissions'],
    });

    if (!userOpunitRole) {
      throw new Error('UserOpunitRole not found');
    }

    // Filter permissions by module and ensure uniqueness
    const uniquePermissions = Array.from(
      new Map(
        userOpunitRole.role.permissions
          .filter((permission) => permission.module === module) // Filter by module
          .map((permission) => [permission.id, permission]), // Ensure uniqueness by ID
      ).values(),
    );

    return uniquePermissions;
  }

  async getPermissionsByUserModule(userId: number, module: string): Promise<Permission[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    if (!user || !user.role) {
      throw new Error('User or Role not found');
    }

    // Filter permissions by module and ensure uniqueness
    const uniquePermissions = Array.from(
      new Map(
        user.role.permissions
          .filter((permission) => permission.module === module) // Filter by module
          .map((permission) => [permission.id, permission]), // Ensure uniqueness by ID
      ).values(),
    );

    return uniquePermissions;
  }
}
