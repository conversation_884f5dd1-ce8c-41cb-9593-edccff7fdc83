import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Publisher } from './entities/publisher.entity';
import { Like, Not, Repository } from 'typeorm';
import { MediaAssetService } from '../media-asset/media-asset.service';
import { CreatePublisherDto } from './dto/publisher-create.dto';
import { UpdatePublisherDto } from './dto/publisher-update.dto';

@Injectable()
export class PublishersService {
  constructor(
    @InjectRepository(Publisher)
    private publisherRepository: Repository<Publisher>,
    private readonly mediaService: MediaAssetService,
  ) {}

  async getPublisherList(
    keyword: string | null = null,
    page: number,
    limit: number,
  ): Promise<{
    data: Publisher[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.publisherRepository.findAndCount({
      where: keyword ? { name: Like(`%${keyword}%`) } : undefined,
      skip: (page - 1) * limit,
      take: limit,
      order: { name: 'ASC' },
    });
    return { data, total, page, limit };
  }

  async getPublisherById(publisherId: number): Promise<Publisher | null> {
    return this.publisherRepository.findOne({ where: { id: publisherId } });
  }

  async validatePublisherName(name: string, publisherId?: number): Promise<boolean> {
    const publisher = await this.publisherRepository.findOne({
      where: publisherId ? { name, id: Not(publisherId) } : { name },
    });
    return !!publisher;
  }

  async createPublisher(publisher: CreatePublisherDto): Promise<Publisher> {
    // Pull Media Asset
    let mediaAsset = undefined;
    if (publisher.logoId) {
      mediaAsset = await this.mediaService.getMediaAssetById(Number(publisher.logoId));
    }
    // Convert empty strings to null
    const cleanPublisher = Object.fromEntries(
      Object.entries(publisher).map(([key, value]) => [key, value === '' ? null : value]),
    );
    //Crete Publisher
    const newPublisher = this.publisherRepository.create({
      name: cleanPublisher.name,
      description: cleanPublisher.description,
      address: cleanPublisher.address,
      city: cleanPublisher.city,
      state: cleanPublisher.state,
      pincode: cleanPublisher.pincode,
      website: cleanPublisher.website,
      emailId: cleanPublisher.emailId,
      mobileNo: cleanPublisher.mobileNo,
      country: cleanPublisher.country,
      logo: mediaAsset,
    });
    return this.publisherRepository.save(newPublisher);
  }

  async updatePublisher(publisherId: number, publisher: UpdatePublisherDto): Promise<Publisher | null> {
    //Check if Publisher Exists
    const publisherExists = await this.publisherRepository.findOne({ where: { id: publisherId } });
    if (!publisherExists) {
      return null;
    }
    // Pull Media Asset
    let mediaAsset = undefined;
    if (publisher.logoId) {
      mediaAsset = await this.mediaService.getMediaAssetById(Number(publisher.logoId));
    }
    // Convert empty strings to null
    const cleanPublisher = Object.fromEntries(
      Object.entries(publisher).map(([key, value]) => [key, value === '' ? null : value]),
    );
    //Update Publisher
    await this.publisherRepository.update(publisherId, {
      name: cleanPublisher.name,
      description: cleanPublisher.description,
      address: cleanPublisher.address,
      city: cleanPublisher.city,
      state: cleanPublisher.state,
      pincode: cleanPublisher.pincode,
      website: cleanPublisher.website,
      emailId: cleanPublisher.emailId,
      mobileNo: cleanPublisher.mobileNo,
      country: cleanPublisher.country,
      logo: mediaAsset,
    });
    // Return Updated Publisher
    return this.publisherRepository.findOne({ where: { id: publisherId } });
  }

  async deletePublisher(publisherId: number): Promise<boolean> {
    const publisher = await this.publisherRepository.findOne({ where: { id: publisherId } });
    if (!publisher) {
      return false;
    }
    await this.publisherRepository.delete({ id: publisherId });
    return true;
  }
}
