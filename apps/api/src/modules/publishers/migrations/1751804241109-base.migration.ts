import { MigrationInterface, QueryRunner } from 'typeorm';
import { PUBLISHER_TABLE_NAME } from '../entities/publisher.entity';
import { MEDIA_ASSET_TABLE_NAME } from '@api/modules/media-asset/entities/media-asset.entity';
import { PERMISSION_TABLE_NAME } from '@api/modules/rbac/entities/permission.entity';

export class PublishersBase_1751804241109 implements MigrationInterface {
  name = 'PublishersBase_1751804241109';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${PUBLISHER_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(128) NOT NULL,
                \`slug\` varchar(64) NOT NULL,
                \`description\` varchar(256) NULL,
                \`address\` varchar(128) NULL,
                \`city\` varchar(64) NULL,
                \`state\` varchar(64) NULL,
                \`pincode\` varchar(16) NULL,
                \`website\` varchar(512) NULL,
                \`email_id\` varchar(128) NULL,
                \`mobile_no\` varchar(16) NULL,
                \`country\` varchar(32) NOT NULL,
                \`logo_id\` int NULL,
                UNIQUE INDEX \`IDX_6095844d564b432ea5ea788353\` (\`slug\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PUBLISHER_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_6aaa0977ffdaf66e946eeddac27\` FOREIGN KEY (\`logo_id\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
          INSERT INTO ${PERMISSION_TABLE_NAME} (slug, name, description, module, \`groups\`, level) VALUES
          ('VIEW_PUBLISHERS', 'View Publishers', 'Can view available publishers.', 'publishers', 'publishers','org'),
          ('MANAGE_PUBLISHERS', 'Manage Publishers', 'Can create, update, view, and delete any publishers.', 'publishers', 'publishers','org');
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('VIEW_PUBLISHERS', 'MANAGE_PUBLISHERS');`,
    );
    await queryRunner.query(`
        DROP INDEX \`IDX_6095844d564b432ea5ea788353\` ON \`${PUBLISHER_TABLE_NAME}\`
    `);
    await queryRunner.query(
      `ALTER TABLE \`${PUBLISHER_TABLE_NAME}\` DROP FOREIGN KEY \`FK_6aaa0977ffdaf66e946eeddac27\``,
    );
    await queryRunner.query(`
            DROP TABLE \`${PUBLISHER_TABLE_NAME}\`
        `);
  }
}
