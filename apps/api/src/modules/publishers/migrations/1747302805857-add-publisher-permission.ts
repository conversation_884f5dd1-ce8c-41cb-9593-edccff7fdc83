import { PERMISSION_TABLE_NAME } from '@/modules/rbac/entities/permission.entity';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPublisherPermission1747302805857 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
          INSERT INTO ${PERMISSION_TABLE_NAME} (slug, name, description, module, groups, level) VALUES
          ('VIEW_PUBLISHERS', 'View Publishers', 'Can view available publishers.', 'publishers', 'publishers','org'),
          ('MANAGE_PUBLISHERS', 'Manage Publishers', 'Can create, update, view, and delete any publishers.', 'publishers', 'publishers','org');`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
          DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('VIEW_PUBLISHERS', 'MANAGE_PUBLISHERS');
        `);
  }
}
