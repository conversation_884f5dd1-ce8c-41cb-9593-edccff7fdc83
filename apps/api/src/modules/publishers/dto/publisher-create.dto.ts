import { <PERSON><PERSON>ptional, Is<PERSON><PERSON>, <PERSON>N<PERSON>ber, IsUrl, IsEmail, Length, ValidateIf } from 'class-validator';

export class CreatePublisherDto {
  @IsOptional()
  @IsNumber({}, { message: 'Logo ID must be a number.' })
  logoId?: number;

  @IsString({ message: 'Name is required and must be a string.' })
  @Length(1, 1024, { message: 'Name must be between 1 and 1024 characters.' })
  name: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string.' })
  @Length(0, 2048, { message: 'Description can be up to 2048 characters.' })
  description?: string;

  @IsOptional()
  @IsString({ message: 'Address must be a string.' })
  @Length(0, 1024, { message: 'Address can be up to 1024 characters.' })
  address?: string;

  @IsOptional()
  @IsString({ message: 'City must be a string.' })
  @Length(0, 1024, { message: 'City can be up to 1024 characters.' })
  city?: string;

  @IsOptional()
  @IsString({ message: 'State must be a string.' })
  @Length(0, 1024, { message: 'State can be up to 1024 characters.' })
  state?: string;

  @IsOptional()
  @IsString({ message: 'Pincode must be a string.' })
  @Length(0, 1024, { message: 'Pincode can be up to 1024 characters.' })
  pincode?: string;

  @IsOptional()
  @IsUrl({}, { message: 'Website must be a valid URL.' })
  @Length(0, 1024, { message: 'Website can be up to 1024 characters.' })
  website?: string;

  @ValidateIf((o: CreatePublisherDto) => o.emailId !== null && o.emailId !== undefined && o.emailId !== '')
  @IsOptional()
  @IsEmail({}, { message: 'Email ID must be a valid email address.' })
  @Length(0, 1024, { message: 'Email ID can be up to 1024 characters.' })
  emailId?: string;

  @IsOptional()
  @IsString({ message: 'Mobile number must be a string.' })
  @Length(0, 1024, { message: 'Mobile number can be up to 1024 characters.' })
  mobileNo?: string;

  @IsString({ message: 'Country is required and must be a string.' })
  @Length(1, 1024, { message: 'Country must be between 1 and 1024 characters.' })
  country: string;
}
