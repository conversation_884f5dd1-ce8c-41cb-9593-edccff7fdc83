import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PublisherListDto } from './dto/publisher-list.dto';
import { PublishersService } from './publishers.service';
import { ApiResponseType } from '@/types/api-response';
import { ApiCookieAuth, ApiTags } from '@nestjs/swagger';
import { AccessGuard } from '../auth/guards/access.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Permissions } from '../auth/decorators/permission.decorator';
import { ListPublisherSwaggerDocs } from './api-docs/list-publisher';
import { CreatePublisherSwaggerDocs } from './api-docs/create-publisher';
import { DetailsPublisherSwaggerDocs } from './api-docs/details-publisher';
import { CreatePublisherDto } from './dto/publisher-create.dto';
import { UpdatePublisherSwaggerDocs } from './api-docs/update-publisher';
import { UpdatePublisherDto } from './dto/publisher-update.dto';
import { DeletePublisherSwaggerDocs } from './api-docs/delete-publisher';

@ApiTags('Publisher Management')
@Controller('publishers')
export class PublishersController {
  constructor(private readonly publishersService: PublishersService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_PUBLISHERS')
  @ApiCookieAuth()
  @ListPublisherSwaggerDocs()
  @Get()
  async list(@Query() query: PublisherListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.publishersService.getPublisherList(keyword, page, limit);
    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No roles found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Roles retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_PUBLISHERS')
  @ApiCookieAuth()
  @DetailsPublisherSwaggerDocs()
  @Get(':publisherId')
  async show(@Param('publisherId') publisherId: string): Promise<ApiResponseType<any>> {
    // Here you would typically fetch the publisher by ID from the database
    const publisher = await this.publishersService.getPublisherById(+publisherId);
    if (!publisher) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Publisher not found',
          data: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Publisher retrieved successfully',
      data: publisher,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_PUBLISHERS')
  @ApiCookieAuth()
  @CreatePublisherSwaggerDocs()
  @Post()
  async create(@Body() createPublisherDto: CreatePublisherDto): Promise<ApiResponseType<any>> {
    console.log(createPublisherDto);
    // Validate Publisher Name
    const validatePublisherName = await this.publishersService.validatePublisherName(createPublisherDto.name);
    if (validatePublisherName) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Publisher name already exists',
          data: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    // Create Publisher
    const publisher = await this.publishersService.createPublisher(createPublisherDto);
    if (!publisher) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Failed to create publisher',
          data: null,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return {
      status: 'success',
      message: 'Publisher retrieved successfully',
      data: publisher,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_PUBLISHERS')
  @ApiCookieAuth()
  @UpdatePublisherSwaggerDocs()
  @Put(':publisherId')
  async update(
    @Param('publisherId') publisherId: string,
    @Body() updatePublisherDto: UpdatePublisherDto,
  ): Promise<ApiResponseType<any>> {
    // Validate Publisher Name
    const validatePublisherName = await this.publishersService.validatePublisherName(
      updatePublisherDto.name,
      +publisherId,
    );
    if (validatePublisherName) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Publisher name already exists',
          data: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    // Update Publisher
    const publisher = await this.publishersService.updatePublisher(+publisherId, updatePublisherDto);
    if (!publisher) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Failed to update publisher',
          data: null,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return {
      status: 'success',
      message: 'Publisher retrieved successfully',
      data: publisher,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_PUBLISHERS')
  @ApiCookieAuth()
  @DeletePublisherSwaggerDocs()
  @Delete(':publisherId')
  async remove(@Param('publisherId') publisherId: string): Promise<ApiResponseType<any>> {
    // Here you would typically delete the publisher by ID from the database
    const publisher = await this.publishersService.getPublisherById(+publisherId);
    if (!publisher) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Publisher not found',
          data: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.publishersService.deletePublisher(+publisherId);
    return {
      status: 'success',
      message: 'Publisher deleted successfully',
      data: null,
    };
  }
}
