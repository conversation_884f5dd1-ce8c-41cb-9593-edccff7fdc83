import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';

export function ListPublisherSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'List publishers' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to search for publishers',
      type: 'string',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      type: 'number',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page',
      type: 'number',
      example: 10,
    }),
    ApiResponse({
      status: 200,
      description: 'Publisher list retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Publisher list retrieved successfully',
          data: {
            items: [
              {
                id: '<number>',
                name: '<string>',
                description: '<string>',
                email: '<string>',
                mobileNo: '<string>',
                address: '<string>',
                avatar: '<string>',
                createdAt: '<string>',
                updatedAt: '<string>',
              },
            ],
            total: '<number>',
            page: '<number>',
            limit: '<number>',
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request.',
      schema: {
        example: {
          status: 'error',
          message: 'Bad request.',
          data: null,
        },
      },
    }),
  );
}
