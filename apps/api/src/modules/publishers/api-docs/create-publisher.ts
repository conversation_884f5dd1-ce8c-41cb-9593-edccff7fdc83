import { applyDecorators } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';

export function CreatePublisherSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new publisher' }),
    ApiBody({
      description: 'Publisher data to be created',
      schema: {
        type: 'object',
        required: ['name', 'country'],
        properties: {
          name: {
            type: 'string',
            description: 'Name of the publisher',
            example: '<PERSON>',
          },
          description: {
            type: 'string',
            description: 'Description of the publisher',
            example: '',
          },
          emailId: {
            type: 'string',
            description: 'Email ID of the publisher',
            example: '',
          },
          mobileNo: {
            type: 'string',
            description: 'Mobile number of the publisher',
            example: '',
          },
          address: {
            type: 'string',
            description: 'Address of the publisher',
            example: '',
          },
          avatar: {
            type: 'string',
            description: 'Avatar Asset ID of the publisher',
            example: '',
          },
          country: {
            type: 'string',
            description: 'Country of the publisher',
            example: 'India',
          },
          logoId: {
            type: 'number',
            description: 'Logo Asset ID of the publisher',
            example: 1,
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Publisher created successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Publisher created successfully',
          data: {
            id: '<number>',
            name: '<string>',
            description: '<string>',
            address: '<string>',
            city: '<string>',
            state: '<string>',
            pincode: '<string>',
            website: '<string>',
            emailId: '<string>',
            mobileNo: '<string>',
            country: '<string>',
            logo: {
              id: '<number>',
              url: '<string>',
            },
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Validation failed for the input data',
      schema: {
        example: {
          statusCode: 400,
          error: 'Bad Request',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict error, e.g., duplicate name',
      schema: {
        example: {
          statusCode: 409,
          error: 'Conflict',
          message: 'Name already exists.',
        },
      },
    }),
    ApiResponse({
      status: 500,
      description: 'Internal server error',
      schema: {
        example: {
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Failed to create publisher due to server error.',
        },
      },
    }),
  );
}
