import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function DeletePublisherSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete a publisher' }),
    ApiResponse({
      status: 200,
      description: 'Publisher deleted successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Publisher deleted successfully',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Publisher not found.',
      schema: {
        example: {
          status: 'error',
          message: 'Publisher not found',
          data: null,
        },
      },
    }),
  );
}
