import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { log } from 'console';

export function DetailsPublisherSwaggerDocs() {
  return applyDecorators(
    ApiOperation({
      summary: 'Get publisher details by ID',
    }),
    ApiParam({
      name: 'publisherId',
      required: true,
      description: 'ID of the publisher',
      type: 'number',
    }),
    ApiResponse({
      status: 200,
      description: 'Publisher details retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Data retrieved successfully',
          data: {
            id: '<number>',
            name: '<string>',
            address: '<string>',
            phone_number: '<string>',
            email: '<string>',
            logo: [],
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request.',
      schema: {
        example: {
          status: 'error',
          message: 'Bad request.',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Publisher not found.',
      schema: {
        example: {
          status: 'error',
          message: 'Publisher not found.',
          data: null,
        },
      },
    }),
  );
}
