import { MediaAsset } from '@api/modules/media-asset/entities/media-asset.entity';
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';

export const PUBLISHER_TABLE_NAME = 'publisher';

@Entity({ name: PUBLISHER_TABLE_NAME })
export class Publisher {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'logo_id' })
  logo: MediaAsset;

  @Column({ type: 'varchar', length: 128 })
  name: string;

  @Column({ type: 'varchar', length: 64, unique: true })
  slug: string;

  @Column({ type: 'varchar', length: 256, nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 128, nullable: true })
  address: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  city: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  state: string;

  @Column({ type: 'varchar', length: 16, nullable: true })
  pincode: string;

  @Column({ type: 'varchar', length: 512, nullable: true })
  website: string;

  @Column({ name: 'email_id', type: 'varchar', length: 128, nullable: true })
  emailId: string;

  @Column({ name: 'mobile_no', type: 'varchar', length: 16, nullable: true })
  mobileNo: string;

  @Column({ type: 'varchar', length: 32 })
  country: string;
}
