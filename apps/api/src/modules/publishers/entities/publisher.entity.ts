import { MediaAsset } from '@/modules/media-asset/entities/media-asset.entity';
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Join<PERSON>olumn } from 'typeorm';

export const PUBLISHER_TABLE_NAME = 'publisher';

@Entity({ name: PUBLISHER_TABLE_NAME })
export class Publisher {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'logo_id' })
  logo: MediaAsset;

  @Column({ type: 'varchar', length: 1024 })
  name: string;

  @Column({ type: 'varchar', length: 2048, nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  address: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  city: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  state: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  pincode: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  website: string;

  @Column({ name: 'email_id', type: 'varchar', length: 1024, nullable: true })
  emailId: string;

  @Column({ name: 'mobile_no', type: 'varchar', length: 1024, nullable: true })
  mobileNo: string;

  @Column({ type: 'varchar', length: 1024 })
  country: string;
}
