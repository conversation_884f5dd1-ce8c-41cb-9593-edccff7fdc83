import { ProductVariation } from '@api/modules/catalog/entities/product-variation.entity';
import { Opunit } from '@api/modules/opunitmgmt/entities/operationalunit.entity';
import { Entity, Column, PrimaryGeneratedColumn, JoinColumn, ManyToOne, Unique } from 'typeorm';

export const STOCK_BALANCE_TABLE_NAME = 'stock_balance';

@Entity({ name: STOCK_BALANCE_TABLE_NAME })
@Unique(['opunitId', 'productVariationId'])
export class StockBalance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'opunit_id', type: 'int' })
  opunitId: number;

  @Column({ name: 'product_variation_id', type: 'int' })
  productVariationId: number;

  @Column({ name: 'available_quantity', type: 'int', unsigned: true })
  availableQuantity: number;

  @Column({ name: 'reserved_quantity', type: 'mediumint', unsigned: true })
  reservedQuantity: number;

  /*==== Relations ====*/
  @ManyToOne(() => Opunit)
  @JoinColumn({ name: 'opunit_id' })
  opunit: Opunit;

  @ManyToOne(() => ProductVariation)
  @JoinColumn({ name: 'product_variation_id' })
  productVariation: ProductVariation;
}
