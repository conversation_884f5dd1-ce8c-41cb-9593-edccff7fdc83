import { ProductVariation } from '@api/modules/catalog/entities/product-variation.entity';
import { Opunit } from '@api/modules/opunitmgmt/entities/operationalunit.entity';
import { Entity, Column, PrimaryGeneratedColumn, JoinColumn, ManyToOne } from 'typeorm';

export const STOCK_LEDGER_TABLE_NAME = 'stock_ledger';

@Entity({ name: STOCK_LEDGER_TABLE_NAME })
export class StockBalance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'opunit_id', type: 'int' })
  opunitId: number;

  @Column({ name: 'product_variation_id', type: 'int' })
  productVariationId: number;

  @Column({ name: 'description', type: 'varchar', length: 1024 })
  description: string;

  @Column({ name: 'previous_quantity', type: 'int', unsigned: true })
  previousQuantity: number;

  @Column({ name: 'operation', type: 'varchar', length: 32 })
  operation: string;

  @Column({ name: 'operation_quantity', type: 'mediumint', unsigned: true })
  operationQuantity: number;

  @Column({ name: 'available_quantity', type: 'int', unsigned: true })
  availableQuantity: number;

  @Column({ name: 'reserved_quantity', type: 'mediumint', unsigned: true })
  reservedQuantity: number;

  /*==== Relations ====*/
  @ManyToOne(() => Opunit)
  @JoinColumn({ name: 'opunit_id' })
  opunit: Opunit;

  @ManyToOne(() => ProductVariation)
  @JoinColumn({ name: 'product_variation_id' })
  productVariation: ProductVariation;
}
