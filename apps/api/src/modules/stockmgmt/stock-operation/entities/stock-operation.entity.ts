import { Opunit } from '@api/modules/opunitmgmt/entities/operationalunit.entity';
import { Entity, Column, PrimaryGeneratedColumn, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { StockOperationItem } from './stock-operation-item.entity';

export const STOCK_OPERATION_TABLE_NAME = 'stock_operation';

/**
 * Direct stock operations, e.g.
 * - Stock Entry
 * - Stock Adjustment/Reconciliation
 * - Stock Removal
 */
@Entity({ name: STOCK_OPERATION_TABLE_NAME })
export class StockOperation {
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Human readable opearion id
   */
  @Column({ name: 'operation_id', type: 'char', length: '17', unique: true, comment: 'e.g. ENT-11JUL25-99999' })
  operationId: string;

  @Column({ name: 'opunit_id', type: 'int' })
  opunitId: number;

  @Column({ name: 'operation_type', type: 'enum', enum: ['ENTRY', 'ADJUSTMENT', 'REMOVAL'] })
  operationType: 'ENTRY' | 'ADJUSTMENT' | 'REMOVAL';

  @Column({ name: 'description', type: 'varchar', length: '512' })
  description: string;

  @Column({ name: 'status', type: 'enum', enum: ['DRAFT', 'APPROVED', 'COMPLETED', 'CANCELLED'] })
  status: 'DRAFT' | 'APPROVED' | 'COMPLETED' | 'CANCELLED';

  @Column({ name: 'executed_on', type: 'datetime', nullable: true })
  executedOn: Date;

  /*==== Relations */
  @OneToMany(() => StockOperationItem, (item) => item.stockOperation)
  items: StockOperationItem[];

  @ManyToOne(() => Opunit)
  @JoinColumn({ name: 'opunit_id' })
  opunit: Opunit;
}
