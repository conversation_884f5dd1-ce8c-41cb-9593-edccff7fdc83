import { ProductVariation } from '@api/modules/catalog/entities/product-variation.entity';
import { Entity, Column, PrimaryGeneratedColumn, JoinColumn, ManyToOne, Unique } from 'typeorm';
import { StockOperation } from './stock-operation.entity';

export const STOCK_OPERATION_ITEM_TABLE_NAME = 'stock_operation_item';

@Entity({ name: STOCK_OPERATION_ITEM_TABLE_NAME })
@Unique(['stockOperationId', 'productVariationId'])
export class StockOperationItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'stock_operation_id', type: 'int' })
  stockOperationId: number;

  @Column({ name: 'product_variation_id', type: 'int' })
  productVariationId: number;

  @Column({ name: 'quantity', type: 'smallint', unsigned: true })
  quantity: number;

  /*==== Relations ====*/
  @ManyToOne(() => StockOperation)
  @JoinColumn({ name: 'stock_operation_id' })
  stockOperation: StockOperation;

  @ManyToOne(() => ProductVariation)
  @JoinColumn({ name: 'product_variation_id' })
  productVariation: ProductVariation;
}
