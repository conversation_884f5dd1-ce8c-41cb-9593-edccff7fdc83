import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber, Min } from 'class-validator';

export class StockOperationItemDTO {
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsNumber()
  productVariationId: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;
}

export class StockOperationDTO {
  operationId: string;

  @IsNotEmpty()
  @IsNumber()
  opunitId: number;

  operationType: 'ENTRY' | 'ADJUSTMENT' | 'REMOVAL';

  description: string;

  status: 'DRAFT' | 'APPROVED' | 'COMPLETED' | 'CANCELLED';

  @IsArray()
  @ArrayMinSize(1)
  items: StockOperationItemDTO[];
}
