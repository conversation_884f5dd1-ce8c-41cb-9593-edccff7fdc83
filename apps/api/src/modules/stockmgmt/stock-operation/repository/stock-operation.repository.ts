import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { StockOperation } from '../entities/stock-operation.entity';

@Injectable()
export class StockOperationRepository extends Repository<StockOperation> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(StockOperation, dataSource.createEntityManager());
  }
}
