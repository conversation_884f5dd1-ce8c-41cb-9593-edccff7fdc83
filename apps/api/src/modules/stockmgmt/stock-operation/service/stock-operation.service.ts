import { Injectable, NotFoundException } from '@nestjs/common';
import { StockOperationDTO } from '../dto/stock-operation-request.dto';
import { StockOperationRepository } from '../repository/stock-operation.repository';

@Injectable()
export class StockOperationService {
  constructor(private readonly repository: StockOperationRepository) {}
  async create(payload: StockOperationDTO) {
    const stockOperation = this.repository.create({
      ...payload,
    });
    return this.repository.save(stockOperation);
  }

  async list() {
    return this.repository.find();
  }

  async details(id: number) {
    return this.repository.findOneBy({ id });
  }

  async update(id: number, payload: StockOperationDTO) {
    const stockOperation = await this.repository.preload({
      id,
      ...payload,
    });
    if (!stockOperation) {
      throw new NotFoundException(`Stock operation with id: ${id} not found`);
    }

    return this.repository.save(stockOperation);
  }

  async remove(id: number) {
    return this.repository.delete(id);
  }
}
