import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function CreateStockOperationApiDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Create new stock operation' }),
    ApiResponse({ status: HttpStatus.CREATED, description: 'Stock Operation created successfully' }),
    ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid request data' }),
  );
}

export function UpdateStockOperationApiDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Update stock operation' }),
    ApiResponse({ status: HttpStatus.OK, description: 'Stock Operation created successfully' }),
    ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid request data' }),
  );
}
