import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { StockOperationService } from '../service/stock-operation.service';
import { StockOperationDTO } from '../dto/stock-operation-request.dto';
import { CreateStockOperationApiDoc, UpdateStockOperationApiDoc } from './api-docs/stock-operation.apidoc';

@Controller('stock-operation')
export class StockOperationController {
  constructor(private readonly service: StockOperationService) {}

  @Post()
  @CreateStockOperationApiDoc()
  async create(@Body() createStockOperationDto: StockOperationDTO) {
    return this.service.create(createStockOperationDto);
  }

  @Get()
  async list() {
    return this.service.list();
  }

  @Get(':id')
  @UpdateStockOperationApiDoc()
  async details(@Param('id') id: string) {
    return this.service.details(+id);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateStockOperationDto: StockOperationDTO) {
    return this.service.update(+id, updateStockOperationDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.service.remove(+id);
  }
}
