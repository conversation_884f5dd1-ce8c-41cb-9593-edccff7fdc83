import { Module } from '@nestjs/common';
import { StockOperationService } from './service//stock-operation.service';
import { StockOperationController } from './controller/stock-operation.controller';
import { StockOperationRepository } from './repository/stock-operation.repository';

@Module({
  controllers: [StockOperationController],
  providers: [StockOperationService, StockOperationRepository],
})
export class StockOperationModule {}
