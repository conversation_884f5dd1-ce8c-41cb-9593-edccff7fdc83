import { MigrationInterface, QueryRunner } from 'typeorm';
import { STOCK_OPERATION_TABLE_NAME } from '../stock-operation/entities/stock-operation.entity';
import { STOCK_OPERATION_ITEM_TABLE_NAME } from '../stock-operation/entities/stock-operation-item.entity';
import { OPUNIT_TABLE_NAME } from '@api/modules/opunitmgmt/entities/operationalunit.entity';
import { STOCK_BALANCE_TABLE_NAME } from '../stock-operation/entities/stock-balance.entity';
import { STOCK_LEDGER_TABLE_NAME } from '../stock-operation/entities/stock-ledger.entity';
import { PRODUCT_VARIATION_TABLE_NAME } from '@api/modules/catalog/entities/product-variation.entity';

export class StockOperationBase_1752227106479 implements MigrationInterface {
  name = 'StockOperationBase_1752227106479';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE \`${STOCK_OPERATION_TABLE_NAME}\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`operation_id\` char(17) NOT NULL COMMENT 'e.g. ENT-11JUL25-99999',
            \`opunit_id\` int NOT NULL,
            \`operation_type\` enum ('ENTRY', 'ADJUSTMENT', 'REMOVAL') NOT NULL,
            \`description\` varchar(512) NOT NULL,
            \`status\` enum ('DRAFT', 'APPROVED', 'COMPLETED', 'CANCELLED') NOT NULL,
            \`executed_on\` DATETIMe,
            UNIQUE INDEX \`IDX_9f5cab030a7e5c86e10bcb2418\` (\`operation_id\`),
            PRIMARY KEY (\`id\`)
        ) ENGINE = InnoDB
    `);

    await queryRunner.query(`
        ALTER TABLE \`${STOCK_OPERATION_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_0a6f5aa3241482e92a88112a357\` FOREIGN KEY (\`opunit_id\`) 
        REFERENCES \`${OPUNIT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
        CREATE TABLE \`${STOCK_OPERATION_ITEM_TABLE_NAME}\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`stock_operation_id\` int NOT NULL,
            \`product_variation_id\` int NOT NULL,
            \`quantity\` smallint UNSIGNED NOT NULL,
            UNIQUE INDEX \`IDX_aaea1b2cebadbc8e2ec2e0d1e6\` (\`stock_operation_id\`, \`product_variation_id\`),
            PRIMARY KEY (\`id\`)
        ) ENGINE = InnoDB
    `);

    await queryRunner.query(`
        ALTER TABLE \`${STOCK_OPERATION_ITEM_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_db80608647b2027536277ae9579\` FOREIGN KEY (\`stock_operation_id\`) 
        REFERENCES \`${STOCK_OPERATION_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_OPERATION_ITEM_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_124ed083b415169c30ca3801edb\` FOREIGN KEY (\`product_variation_id\`) 
        REFERENCES \`${PRODUCT_VARIATION_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
        CREATE TABLE \`${STOCK_BALANCE_TABLE_NAME}\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`opunit_id\` int NOT NULL,
            \`product_variation_id\` int NOT NULL,
            \`available_quantity\` int UNSIGNED NOT NULL,
            \`reserved_quantity\` mediumint UNSIGNED NOT NULL,
            UNIQUE INDEX \`IDX_0b774a29a4c507aef7c20b2813\` (\`opunit_id\`, \`product_variation_id\`),
            PRIMARY KEY (\`id\`)
        ) ENGINE = InnoDB
    `);

    await queryRunner.query(`
        ALTER TABLE \`${STOCK_BALANCE_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_18aeccbca05502f16c9fdd0d533\` FOREIGN KEY (\`opunit_id\`) 
        REFERENCES \`${OPUNIT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
        ALTER TABLE \`${STOCK_BALANCE_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_217bd7789fb7942965286027c50\` FOREIGN KEY (\`product_variation_id\`) 
        REFERENCES \`${PRODUCT_VARIATION_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
        CREATE TABLE \`${STOCK_LEDGER_TABLE_NAME}\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`opunit_id\` int NOT NULL,
            \`product_variation_id\` int NOT NULL,
            \`description\` varchar(1024) NOT NULL,
            \`previous_quantity\` int UNSIGNED NOT NULL,
            \`operation\` varchar(32) NOT NULL,
            \`operation_quantity\` mediumint UNSIGNED NOT NULL,
            \`available_quantity\` int UNSIGNED NOT NULL,
            \`reserved_quantity\` mediumint UNSIGNED NOT NULL,
            PRIMARY KEY (\`id\`)
        ) ENGINE = InnoDB
    `);

    await queryRunner.query(`
        ALTER TABLE \`${STOCK_LEDGER_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_b04d3e00ea74714cdcfb9599189\` FOREIGN KEY (\`opunit_id\`) 
        REFERENCES \`${OPUNIT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
        ALTER TABLE \`${STOCK_LEDGER_TABLE_NAME}\`
        ADD CONSTRAINT \`FK_e39d0ebf215901553d2f0e17bc6\` FOREIGN KEY (\`product_variation_id\`) 
        REFERENCES \`${PRODUCT_VARIATION_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_LEDGER_TABLE_NAME}\` DROP FOREIGN KEY \`FK_e39d0ebf215901553d2f0e17bc6\`
    `);
    await queryRunner.query(
      `ALTER TABLE \`${STOCK_LEDGER_TABLE_NAME}\` DROP FOREIGN KEY \`FK_b04d3e00ea74714cdcfb9599189\``,
    );
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_BALANCE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_217bd7789fb7942965286027c50\`
    `);
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_BALANCE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_18aeccbca05502f16c9fdd0d533\`
    `);
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_OPERATION_TABLE_NAME}\` DROP FOREIGN KEY \`FK_0a6f5aa3241482e92a88112a357\`
    `);
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_OPERATION_ITEM_TABLE_NAME}\` DROP FOREIGN KEY \`FK_124ed083b415169c30ca3801edb\`
    `);
    await queryRunner.query(`
        ALTER TABLE \`${STOCK_OPERATION_ITEM_TABLE_NAME}\` DROP FOREIGN KEY \`FK_db80608647b2027536277ae9579\`
    `);
    await queryRunner.query(`
        DROP TABLE \`${STOCK_LEDGER_TABLE_NAME}\`
    `);
    await queryRunner.query(`
        DROP INDEX \`IDX_0b774a29a4c507aef7c20b2813\` ON \`${STOCK_BALANCE_TABLE_NAME}\`
    `);
    await queryRunner.query(`
        DROP TABLE \`${STOCK_BALANCE_TABLE_NAME}\`
    `);
    await queryRunner.query(`
        DROP INDEX \`IDX_9f5cab030a7e5c86e10bcb2418\` ON \`${STOCK_OPERATION_TABLE_NAME}\`
    `);
    await queryRunner.query(`
        DROP TABLE \`${STOCK_OPERATION_TABLE_NAME}\`
    `);
    await queryRunner.query(`
        DROP INDEX \`IDX_aaea1b2cebadbc8e2ec2e0d1e6\` ON \`${STOCK_OPERATION_ITEM_TABLE_NAME}\`
    `);
    await queryRunner.query(`
        DROP TABLE \`${STOCK_OPERATION_ITEM_TABLE_NAME}\`
    `);
  }
}
