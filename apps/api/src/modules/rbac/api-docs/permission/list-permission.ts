import { ApiResponse, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ApiPermissionListSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get All Permissions list' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      type: String,
      description: 'Keyword to filter permissions by name',
    }),
    ApiResponse({
      status: 200,
      description: 'Permissions retrieved successfully',
      schema: {
        example: {
          rbac: {
            default: [
              {
                id: '<number>',
                slug: '<string>',
                name: '<string>',
                description: '<string>',
                module: '<string>',
                groups: [],
                level: '<string>',
              },
              {
                id: '<number>',
                slug: '<string>',
                name: '<string>',
                description: '<string>',
                module: '<string>',
                groups: [],
                level: '<string>',
              },
            ],
          },
          usermgmt: {
            user: [
              {
                id: '<number>',
                slug: '<string>',
                name: '<string>',
                description: '<string>',
                module: '<string>',
                groups: '<string>',
                level: '<string>',
              },
            ],
          },
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the opunit',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the opunit',
          data: [],
        },
      },
    }),
  );
}
