import { ApiResponse, ApiParam, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ApiRoleDetailsSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get Role Details' }),
    ApiParam({
      name: 'id',
      required: true,
      type: Number,
      description: 'ID of the role to retrieve details for',
    }),
    ApiResponse({
      status: 200,
      description: 'Role details retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Role details retrieved successfully',
          data: {
            id: '<number>',
            name: '<string>',
            description: '<string>',
            isReadOnly: '<boolean>',
            rolePosition: '<number>',
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Role not found',
      schema: {
        example: {
          status: 'error',
          message: 'Role not found',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Unauthorized access',
      schema: {
        example: {
          status: 'error',
          message: 'Unauthorized access',
          data: null,
        },
      },
    }),
  );
}
