import { ApiResponse, ApiParam, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ApiRoleDeleteSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete Role' }),
    ApiParam({
      name: 'id',
      required: true,
      type: Number,
      description: 'ID of the role to delete',
    }),
    ApiResponse({
      status: 200,
      description: 'Role deleted successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Role deleted successfully',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Role not found',
      schema: {
        example: {
          status: 'error',
          message: 'No role found',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Unauthorized access',
      schema: {
        example: {
          status: 'error',
          message: 'Unauthorized access',
          data: null,
        },
      },
    }),
  );
}
