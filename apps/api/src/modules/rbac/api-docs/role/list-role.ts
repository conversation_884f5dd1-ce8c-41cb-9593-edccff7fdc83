import { ApiResponse, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ApiRoleListSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get All Roles list' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      type: String,
      description: 'Keyword to filter roles by name',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page for pagination',
      example: 10,
    }),

    ApiResponse({
      status: 200,
      description: 'Roles retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Roles retrieved successfully',
          data: {
            items: [
              {
                id: '<number>',
                name: '<string>',
                description: '<string>',
                isReadOnly: '<boolean>',
                rolePosition: '<number>',
              },
            ],
            total: 1,
            page: 1,
            limit: 10,
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'No roles found',
      schema: {
        example: {
          status: 'error',
          message: 'No roles found',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the get role list',
          data: [],
        },
      },
    }),
  );
}
