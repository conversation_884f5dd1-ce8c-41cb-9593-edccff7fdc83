import { Api<PERSON>ody, ApiParam, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';

export function ApiRoleUpdateSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Update Role' }),
    ApiParam({
      name: 'id',
      required: true,
      type: Number,
      description: 'ID of the role to update',
    }),
    ApiBody({
      description: 'Role update payload',
      schema: {
        type: 'object',
        required: ['name', 'is_read_only', 'permissions'],
        properties: {
          name: {
            type: 'string',
            example: 'Admin',
            description: 'Name of the role',
          },
          description: {
            type: 'string',
            example: 'Administration Responsible',
            description: 'Description of the role',
          },
          is_read_only: {
            type: 'boolean',
            example: true,
            description: 'Is the role read-only?',
          },
          permissions: {
            type: 'array',
            example: [1, 2, 3],
            description: 'Array of permission IDs to be associated with the role',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Role updated successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Role updated successfully',
          data: {
            id: '<number>',
            name: '<string>',
            description: '<string>',
            isReadOnly: '<boolean>',
            rolePosition: '<number>',
            permissions: [
              {
                id: '<number>',
                slug: '<string>',
                name: '<string>',
                description: '<string>',
                module: '<string>',
                groups: [],
                level: '<string>',
              },
            ],
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'No role found',
      schema: {
        example: {
          status: 'error',
          message: 'No role found',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Validation failed or role name already exists',
      schema: {
        example: {
          statusCode: 400,
          error: 'Bad Request',
          message: [
            {
              property: 'name',
              errors: ['The name field is required and cannot be empty.'],
            },
            {
              property: 'description',
              errors: ['The description must not exceed 1024 characters..'],
            },
            {
              property: 'is_read_only',
              errors: ['The is_read_only field must be a boolean value.'],
            },
            {
              property: 'permissions',
              errors: ['The permissions field is required and cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Role name already exists',
      schema: {
        example: {
          status: 'error',
          message: 'Role name already exists',
        },
      },
    }),
    ApiResponse({
      status: 401,
      description: 'Error: Unauthorized',
      schema: {
        example: {
          status: 'error',
          message: 'User does not have access to the update role',
          data: [],
        },
      },
    }),
  );
}
