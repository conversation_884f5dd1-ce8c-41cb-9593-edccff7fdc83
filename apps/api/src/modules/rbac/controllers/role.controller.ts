import {
  Controller,
  Get,
  Query,
  HttpException,
  HttpStatus,
  Param,
  Body,
  Post,
  Put,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiCookieAuth } from '@nestjs/swagger';
import { RoleService } from '../services/role.service';
import { RoleListDto } from '../dto/role-list.dto';
import { ApiResponseType } from '@common-utils/api-types';
import { CreateRoleDto } from '../dto/create-role.dto';
import { UpdateRoleDto } from '../dto/update-role.dto';
import { JwtAuthGuard } from '@api/modules/auth/guards/jwt-auth.guard';
import { AccessGuard } from '@api/modules/auth/guards/access.guard';
import { Permissions } from '@api/modules/auth/decorators/permission.decorator';
import { ApiRoleListSwaggerDocs } from '../api-docs/role/list-role';
import { ApiRoleDetailsSwaggerDocs } from '../api-docs/role/details-role';
import { ApiRoleCreateSwaggerDocs } from '../api-docs/role/create-role';
import { ApiRoleUpdateSwaggerDocs } from '../api-docs/role/update-role';
import { ApiRoleDeleteSwaggerDocs } from '../api-docs/role/delete-role';

@ApiTags('Roles')
@Controller('roles')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_ROLES')
  @ApiCookieAuth()
  @ApiRoleListSwaggerDocs()
  @Get()
  async getRoleList(@Query() query: RoleListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.roleService.getRoleList(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No roles found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Roles retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_ROLES')
  @ApiCookieAuth()
  @ApiRoleDetailsSwaggerDocs()
  @Get('/:roleId')
  async getRoleById(@Param('roleId') roleId: number): Promise<ApiResponseType<any>> {
    const role = await this.roleService.getRoleById(roleId);
    if (!role) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No role found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Role retrieved successfully',
      data: role,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_ROLE')
  @ApiCookieAuth()
  @ApiRoleCreateSwaggerDocs()
  @Post()
  async createRole(@Body() data: CreateRoleDto): Promise<ApiResponseType<any>> {
    // Check if the role name already exists
    const existingRole = await this.roleService.getRoleByName(data.name);
    if (existingRole) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Role name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    // Proceed to create the role
    const role = await this.roleService.createRole(data);
    return {
      status: 'success',
      message: 'Role created successfully',
      data: role,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_ROLE')
  @ApiCookieAuth()
  @ApiRoleUpdateSwaggerDocs()
  @Put('/:roleId')
  async updateRole(@Param('roleId') roleId: number, @Body() data: UpdateRoleDto): Promise<ApiResponseType<any>> {
    //Validate the id passed have a role or not
    const checkRole = await this.roleService.getRoleById(roleId);
    if (!checkRole) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No role found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    // Check if the role name already exists
    const existingRole = await this.roleService.getRoleByName(data.name);
    if (existingRole && existingRole.id !== roleId) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Role name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }

    // Proceed to update the role
    const role = await this.roleService.updateRole(roleId, data);
    return {
      status: 'success',
      message: 'Role updated successfully',
      data: role,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_ROLE')
  @ApiCookieAuth()
  @ApiRoleDeleteSwaggerDocs()
  @Delete('/:roleId')
  async deleteRole(@Param('roleId') roleId: number): Promise<ApiResponseType<any>> {
    //Validate the id passed have a role or not
    const checkRole = await this.roleService.getRoleById(roleId);
    if (!checkRole) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No role found',
          data: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    // Proceed to delete the role
    await this.roleService.deleteRole(roleId);
    return {
      status: 'success',
      message: 'Role deleted successfully',
      data: null,
    };
  }
}
