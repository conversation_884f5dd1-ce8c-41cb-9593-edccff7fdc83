import {
  Controller,
  Get,
  Query,
  HttpException,
  HttpStatus,
  Param,
  Body,
  Post,
  Put,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiQuery, ApiResponse, ApiOperation, ApiBody, ApiCookieAuth } from '@nestjs/swagger';
import { RoleService } from '../services/role.service';
import { RoleListDto } from '../dto/role-list.dto';
import { ApiResponseType } from 'src/types/api-response';
import { CreateRoleDto } from '../dto/create-role.dto';
import { UpdateRoleDto } from '../dto/update-role.dto';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { AccessGuard } from '@/modules/auth/guards/access.guard';
import { Permissions } from '@/modules/auth/decorators/permission.decorator';

@ApiTags('Roles')
@Controller('roles')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_ROLES')
  @ApiCookieAuth()
  @Get()
  @ApiOperation({ summary: 'Get list of Roles' })
  @ApiQuery({
    name: 'keyword',
    required: false,
    description: 'Keyword to filter data by name',
  })
  @ApiQuery({
    name: 'page',
    required: true,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: true,
    description: 'Number of items per page for pagination',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Roles retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Roles retrieved successfully',
        data: {
          items: [
            {
              id: 1,
              name: 'Admin',
              description: 'Administrator role',
            },
            {
              id: 2,
              name: 'User',
              description: 'Regular user role',
            },
          ],
          total: 50,
          page: 1,
          limit: 10,
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No roles found',
    schema: {
      example: {
        status: 'error',
        message: 'No roles found',
        data: [],
      },
    },
  })
  async getRoleList(@Query() query: RoleListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.roleService.getRoleList(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No roles found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Roles retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_ROLES')
  @ApiCookieAuth()
  @Get('/:id')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiResponse({
    status: 200,
    description: 'Role retrieved successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Role retrieved successfully',
        data: {
          id: 1,
          name: 'Admin',
          description: 'Administrator role',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No role found',
    schema: {
      example: {
        status: 'error',
        message: 'No role found',
        data: [],
      },
    },
  })
  async getRoleById(@Param('id') id: number): Promise<ApiResponseType<any>> {
    const role = await this.roleService.getRoleById(id);
    if (!role) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No role found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Role retrieved successfully',
      data: role,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_ROLE')
  @ApiCookieAuth()
  @Post()
  @ApiOperation({ summary: 'Create role' })
  @ApiBody({
    description: 'Data for creating a new role',
    schema: {
      example: {
        name: 'Admin',
        description: 'Administrator role',
        is_read_only: false,
        permissions: [1, 2, 3],
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Role created successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Role created successfully',
        data: {
          id: 1,
          name: 'Admin',
          description: 'Administrator role',
        },
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Role name already exists',
    schema: {
      example: {
        status: 'error',
        message: 'Role name already exists',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Validation failed or role name already exists',
    schema: {
      example: {
        statusCode: 400,
        error: 'Bad Request',
        message: [
          {
            property: 'name',
            errors: ['The name field is required and cannot be empty.'],
          },
          {
            property: 'is_read_only',
            errors: ['The is_read_only field must be a boolean value.'],
          },
          {
            property: 'role_position',
            errors: ['The role_position must be at least 0.', 'The role_position must be an integer.'],
          },
        ],
      },
    },
  })
  async createRole(@Body() data: CreateRoleDto): Promise<ApiResponseType<any>> {
    // Check if the role name already exists
    const existingRole = await this.roleService.getRoleByName(data.name);
    if (existingRole) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Role name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    // Proceed to create the role
    const role = await this.roleService.createRole(data);
    return {
      status: 'success',
      message: 'Role created successfully',
      data: role,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_ROLE')
  @ApiCookieAuth()
  @Put('/:id')
  @ApiOperation({ summary: 'Update role' })
  @ApiBody({
    description: 'Data for updating an existing role',
    schema: {
      example: {
        name: 'Admin',
        description: 'Administrator role',
        is_read_only: false,
        role_position: 1,
        permissions: [1, 2, 3],
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Role updated successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Role updated successfully',
        data: {
          id: 1,
          name: 'Admin',
          description: 'Administrator role',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No role found',
    schema: {
      example: {
        status: 'error',
        message: 'No role found',
        data: [],
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Role name already exists',
    schema: {
      example: {
        status: 'error',
        message: 'Role name already exists',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Validation failed or role name already exists',
    schema: {
      example: {
        statusCode: 400,
        error: 'Bad Request',
        message: [
          {
            property: 'name',
            errors: ['The name field is required and cannot be empty.'],
          },
          {
            property: 'is_read_only',
            errors: ['The is_read_only field must be a boolean value.'],
          },
          {
            property: 'role_position',
            errors: ['The role_position must be at least 0.', 'The role_position must be an integer.'],
          },
        ],
      },
    },
  })
  async updateRole(@Param('id') id: number, @Body() data: UpdateRoleDto): Promise<ApiResponseType<any>> {
    //Validate the id passed have a role or not
    const checkRole = await this.roleService.getRoleById(id);
    if (!checkRole) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No role found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    // Check if the role name already exists
    const existingRole = await this.roleService.getRoleByName(data.name);
    if (existingRole && existingRole.id !== id) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Role name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }

    // Proceed to update the role
    const role = await this.roleService.updateRole(id, data);
    return {
      status: 'success',
      message: 'Role updated successfully',
      data: role,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_ROLE')
  @ApiCookieAuth()
  @Delete('/:id')
  @ApiOperation({ summary: 'Delete role' })
  @ApiResponse({
    status: 200,
    description: 'Role deleted successfully',
    schema: {
      example: {
        status: 'success',
        message: 'Role deleted successfully',
        data: null,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No role found',
    schema: {
      example: {
        status: 'error',
        message: 'No role found',
        data: [],
      },
    },
  })
  async deleteRole(@Param('id') id: number): Promise<ApiResponseType<any>> {
    //Validate the id passed have a role or not
    const checkRole = await this.roleService.getRoleById(id);
    if (!checkRole) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No role found',
          data: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    // Proceed to delete the role
    await this.roleService.deleteRole(id);
    return {
      status: 'success',
      message: 'Role deleted successfully',
      data: null,
    };
  }
}
