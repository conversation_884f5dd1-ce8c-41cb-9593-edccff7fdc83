import { Controller, Get, Query, HttpException, HttpStatus, UseGuards } from '@nestjs/common';
import { ApiTags, ApiCookieAuth } from '@nestjs/swagger';
import { PermissionService } from '../services/permission.service';
import { PermissionListDto } from '../dto/permission-list.dto';
import { ApiResponseType } from '@common-utils/api-types';
import { JwtAuthGuard } from '@api/modules/auth/guards/jwt-auth.guard';
import { ApiPermissionListSwaggerDocs } from '../api-docs/permission/list-permission';
@ApiTags('Permissions') // Swagger tag for grouping
@Controller('permissions')
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @UseGuards(JwtAuthGuard)
  @ApiCookieAuth()
  @ApiPermissionListSwaggerDocs()
  @Get()
  async getPermissionList(@Query() query: PermissionListDto): Promise<ApiResponseType<any>> {
    const { keyword } = query;
    const permissions = await this.permissionService.getPermissionList(keyword ?? null);

    if (!permissions || permissions.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No permissions found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    // Group permissions by module
    const groupedPermissions = permissions.reduce(
      (grouped, permission) => {
        const module = permission.module || 'default';
        if (!grouped[module]) {
          grouped[module] = {};
        }

        const group = permission.groups || 'default';
        if (!grouped[module][group]) {
          grouped[module][group] = [];
        }

        grouped[module][group].push(permission);
        return grouped;
      },
      {} as Record<string, Record<string, any[]>>,
    );

    return {
      status: 'success',
      message: 'Permissions retrieved successfully',
      data: groupedPermissions,
    };
  }
}
