import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

export const PERMISSION_TABLE_NAME = 'permission';
@Entity({ name: PERMISSION_TABLE_NAME })
export class Permission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 32, unique: true })
  slug: string;

  @Column({ type: 'varchar', length: 128 })
  name: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 128 })
  module: string;

  @Column({ type: 'varchar', length: 256, nullable: true })
  groups: string;

  /**
   * SUPER_ADMIN can see all, app,org,opunit
   * User have Org level role can see all, org,opunit
   * User have Opunit level role can see all, opunit
   */
  @Column({ type: 'enum', enum: ['all', 'app', 'org', 'opunit'], default: 'all' })
  level: string;
}
