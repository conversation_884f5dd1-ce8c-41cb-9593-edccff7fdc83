import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, ManyToMany, JoinTable } from 'typeorm';
import { Permission } from './permission.entity';

export const ROLE_TABLE_NAME = 'role';
@Entity({ name: ROLE_TABLE_NAME })
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 128, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  description: string;

  @Column({ name: 'is_read_only', type: 'boolean', default: false })
  isReadOnly: boolean;

  @Column({ name: 'role_position', type: 'smallint' })
  rolePosition: number;

  @ManyToMany(() => Permission)
  @JoinTable({
    name: 'role_has_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
  })
  permissions: Permission[];
}
