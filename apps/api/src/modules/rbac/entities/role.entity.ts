import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToMany, JoinTable } from 'typeorm';
import { Permission } from './permission.entity';

export const ROLE_TABLE_NAME = 'rbac_role';
export const ROLE_PERMISSION_TABLE_NAME = 'rbac_role_has_permissions';

@Entity({ name: ROLE_TABLE_NAME })
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 64, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 128, nullable: true })
  description: string;

  @Column({ name: 'is_read_only', type: 'boolean', default: false })
  isReadOnly: boolean;

  @Column({ name: 'role_position', type: 'smallint' })
  rolePosition: number;

  @ManyToMany(() => Permission)
  @JoinTable({
    name: ROLE_PERMISSION_TABLE_NAME,
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
  })
  permissions: Permission[];
}
