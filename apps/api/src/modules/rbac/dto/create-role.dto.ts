import { <PERSON><PERSON><PERSON>, IsB<PERSON>ean, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON> } from 'class-validator';

export class CreateRoleDto {
  @IsString({ message: 'The name must be a string.' })
  @IsNotEmpty({ message: 'The name field is required and cannot be empty.' })
  @MaxLength(128, { message: 'The name must not exceed 128 characters.' })
  name: string;

  @IsString({ message: 'The description must be a string.' })
  @IsNotEmpty({
    message: 'The description field is required and cannot be empty.',
  })
  @MaxLength(1024, {
    message: 'The description must not exceed 1024 characters.',
  })
  description: string;

  @IsBoolean({ message: 'The is_read_only field must be a boolean value.' })
  @IsNotEmpty({
    message: 'The is_read_only field is required and cannot be empty.',
  })
  is_read_only: boolean;

  @IsArray({ message: 'The permissions must be an array of integers.' })
  @IsInt({ each: true, message: 'Each permission ID must be an integer.' })
  @IsNotEmpty({
    message: 'The permissions field is required and cannot be empty.',
  })
  permissions: number[];
}
