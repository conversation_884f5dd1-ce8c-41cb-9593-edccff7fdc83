import { MigrationInterface, QueryRunner } from 'typeorm';
import { PERMISSION_TABLE_NAME } from '../entities/permission.entity';
import { ROLE_TABLE_NAME } from '../entities/role.entity';

export class RBACSeeder_1751803020772 implements MigrationInterface {
  name = 'RBACSeeder_1751803020772';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO ${PERMISSION_TABLE_NAME} (slug,name,description, module) VALUES
             ('MANAGE_ROLE', 'Manage Role','Can create role,update view delete any role.','rbac'),
             ('VIEW_ROLES',  'View Roles','Can view available roles','rbac');`,
    );
    await queryRunner.query(
      `INSERT INTO ${ROLE_TABLE_NAME} (name, description,is_read_only,role_position) VALUES
            ('Administrator','Administrator', true, 10)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM ${ROLE_TABLE_NAME} WHERE name='Administrator';`);
    await queryRunner.query(`
        DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('MANAGE_ROLE', 'VIEW_ROLES');
       `);
  }
}
