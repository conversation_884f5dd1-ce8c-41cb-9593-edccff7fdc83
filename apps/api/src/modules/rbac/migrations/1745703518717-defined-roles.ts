import { MigrationInterface, QueryRunner } from 'typeorm';
import { ROLE_TABLE_NAME } from '../entities/role.entity';

export class DefinedRoles1745703518717 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`INSERT INTO ${ROLE_TABLE_NAME} (name, description,is_read_only,role_position) VALUES
      ('Administrator','Administrator', true, 10)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM ${ROLE_TABLE_NAME} WHERE name='Administrator';`);
  }
}
