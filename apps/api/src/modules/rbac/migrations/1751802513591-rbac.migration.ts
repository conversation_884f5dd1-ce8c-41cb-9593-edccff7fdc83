import { MigrationInterface, QueryRunner } from 'typeorm';
import { ROLE_TABLE_NAME, ROLE_PERMISSION_TABLE_NAME } from '../entities/role.entity';
import { PERMISSION_TABLE_NAME } from '../entities/permission.entity';

export class RBAC_1751802513591 implements MigrationInterface {
  name = 'RBAC_1751802513591';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${PERMISSION_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`slug\` varchar(32) NOT NULL,
                \`name\` varchar(128) NOT NULL,
                \`description\` varchar(256) NULL,
                \`module\` varchar(32) NOT NULL,
                \`groups\` varchar(32) NULL,
                \`level\` enum ('all', 'app', 'org', 'opunit') NOT NULL DEFAULT 'all',
                UNIQUE INDEX \`IDX_fdd3887e887adfeafde17a31a5\` (\`slug\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${ROLE_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(64) NOT NULL,
                \`description\` varchar(128) NULL,
                \`is_read_only\` tinyint NOT NULL DEFAULT 0,
                \`role_position\` smallint NOT NULL,
                UNIQUE INDEX \`IDX_fce815850f9d9ef177a31aafc0\` (\`name\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            CREATE TABLE \`${ROLE_PERMISSION_TABLE_NAME}\` (
                \`role_id\` int NOT NULL,
                \`permission_id\` int NOT NULL,
                INDEX \`IDX_2d77bb8868f2d6d38e1566ab2f\` (\`role_id\`),
                INDEX \`IDX_fcfb4bae3877619dd8ae32d2e0\` (\`permission_id\`),
                PRIMARY KEY (\`role_id\`, \`permission_id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${ROLE_PERMISSION_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_2d77bb8868f2d6d38e1566ab2fc\` FOREIGN KEY (\`role_id\`) REFERENCES \`${ROLE_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
    await queryRunner.query(`
            ALTER TABLE \`${ROLE_PERMISSION_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_fcfb4bae3877619dd8ae32d2e06\` FOREIGN KEY (\`permission_id\`) REFERENCES \`${PERMISSION_TABLE_NAME}\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`${ROLE_PERMISSION_TABLE_NAME}\` DROP FOREIGN KEY \`FK_fcfb4bae3877619dd8ae32d2e06\`
        `);
    await queryRunner.query(`
            ALTER TABLE \`${ROLE_PERMISSION_TABLE_NAME}\` DROP FOREIGN KEY \`FK_2d77bb8868f2d6d38e1566ab2fc\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_fcfb4bae3877619dd8ae32d2e0\` ON \`${ROLE_PERMISSION_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_2d77bb8868f2d6d38e1566ab2f\` ON \`${ROLE_PERMISSION_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${ROLE_PERMISSION_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_fce815850f9d9ef177a31aafc0\` ON \`${ROLE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${ROLE_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP INDEX \`IDX_fdd3887e887adfeafde17a31a5\` ON \`${PERMISSION_TABLE_NAME}\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PERMISSION_TABLE_NAME}\`
        `);
  }
}
