import { Injectable } from '@nestjs/common';
import { Role } from '../entities/role.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Like, Not, Repository } from 'typeorm';
import { CreateRoleDto } from '../dto/create-role.dto';
import { Permission } from '../entities/permission.entity';
import { UpdateRoleDto } from '../dto/update-role.dto';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async getRoleList(
    keyword: string | null = null,
    page: number,
    limit: number,
  ): Promise<{
    data: Role[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.roleRepository.findAndCount({
      where: keyword ? { name: Like(`%${keyword}%`) } : undefined,
      skip: (page - 1) * limit,
      take: limit,
      order: { name: 'ASC' },
    });
    return { data, total, page, limit };
  }

  async getAllRoles(): Promise<Role[]> {
    return this.roleRepository.find({
      order: { name: 'ASC' },
      where: { id: Not(1) },
      relations: ['permissions'],
    });
  }

  async getRoleById(id: number): Promise<Role | null> {
    return await this.roleRepository.findOne({
      where: { id },
      relations: ['permissions'],
    });
  }

  async createRole(roleData: CreateRoleDto): Promise<Role> {
    const { permissions, is_read_only, ...roleDetails } = roleData;

    // Fetch Permission entities if permissions are provided
    let permissionEntities: Permission[] = [];
    if (permissions && permissions.length > 0) {
      permissionEntities = await this.roleRepository.manager.find(Permission, {
        where: { id: In(permissions) }, // Use the In operator to match multiple IDs
      });
    }
    // Get the maximum role_position value and increment it by 1
    const lastRole = await this.roleRepository.findOne({
      where: {},
      order: { rolePosition: 'DESC' },
    });

    const newRolePosition = lastRole ? lastRole.rolePosition + 1 : 1;
    // Create the Role entity
    const role = this.roleRepository.create({
      ...roleDetails,
      isReadOnly: is_read_only, // Explicitly map is_read_only to isReadOnly
      rolePosition: newRolePosition,
      permissions: permissionEntities,
    });
    // Save the Role entity
    return await this.roleRepository.save(role);
  }

  async updateRole(id: number, roleData: UpdateRoleDto): Promise<Role> {
    const { permissions, is_read_only, ...roleDetails } = roleData;
    let permissionEntities: Permission[] = [];
    if (permissions && permissions.length > 0) {
      permissionEntities = await this.roleRepository.manager.find(Permission, {
        where: { id: In(permissions) }, // Use the In operator to match multiple IDs
      });
    }
    const role = await this.roleRepository.findOne({ where: { id } });
    if (!role) {
      throw new Error('Role not found');
    }
    role.name = roleDetails.name;
    role.description = roleDetails.description;
    role.isReadOnly = is_read_only;
    role.permissions = permissionEntities;
    return await this.roleRepository.save(role);
  }

  async getRoleByName(name: string): Promise<Role | null> {
    return this.roleRepository.findOne({ where: { name } });
  }

  async deleteRole(id: number): Promise<void> {
    const role = await this.roleRepository.findOne({ where: { id } });
    if (!role) {
      throw new Error('Role not found');
    }
    await this.roleRepository.remove(role);
  }

  async getAccessByRole(roleId: number): Promise<Permission[]> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });
    if (!role) {
      throw new Error('Role not found');
    }
    return role.permissions;
  }
}
