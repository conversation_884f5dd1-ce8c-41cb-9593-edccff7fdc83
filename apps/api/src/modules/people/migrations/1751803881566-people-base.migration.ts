import { MigrationInterface, QueryRunner } from 'typeorm';
import { PEOPLE_TABLE_NAME } from '../entities/people.entity';
import { MEDIA_ASSET_TABLE_NAME } from '@api/modules/media-asset/entities/media-asset.entity';
import { PERMISSION_TABLE_NAME } from '@api/modules/rbac/entities/permission.entity';

export class PeopleBase_1751803881566 implements MigrationInterface {
  name = 'PeopleBase_1751803881566';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${PEOPLE_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(256) NOT NULL,
                \`slug\` varchar(64) NOT NULL,
                \`mobile_no\` varchar(16) NULL,
                \`email\` varchar(64) NULL,
                \`description\` varchar(1024) NULL,
                \`avatar\` int NULL,
                UNIQUE INDEX \`IDX_fee4240b16b144248bd7bf7355\` (\`slug\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${PEOPLE_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_d5929035bcab537a8e053dcc540\` FOREIGN KEY (\`avatar\`) REFERENCES \`${MEDIA_ASSET_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

    await queryRunner.query(
      `INSERT INTO ${PERMISSION_TABLE_NAME} (slug, name, description, module, \`groups\`, level) VALUES
                   ('MANAGE_PEOPLE', 'Manage People', 'Can create, update, view, and delete any people.', 'people', 'people','org'),
                   ('VIEW_PEOPLE', 'View People', 'Can view available people.', 'people', 'people','org');`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('MANAGE_PEOPLE', 'VIEW_PEOPLE');`);
    await queryRunner.query(`
        DROP INDEX \`IDX_fee4240b16b144248bd7bf7355\` ON \`${PEOPLE_TABLE_NAME}\`
    `);
    await queryRunner.query(`
            ALTER TABLE \`${PEOPLE_TABLE_NAME}\` DROP FOREIGN KEY \`FK_d5929035bcab537a8e053dcc540\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${PEOPLE_TABLE_NAME}\`
        `);
  }
}
