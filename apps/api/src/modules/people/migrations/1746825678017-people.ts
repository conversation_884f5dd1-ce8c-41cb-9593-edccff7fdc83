import { MigrationInterface, QueryRunner } from 'typeorm';

export class People1746825678017 implements MigrationInterface {
  name = 'People1746825678017';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`people\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(256) NOT NULL,
                \`mobile_no\` varchar(256) NULL,
                \`email\` varchar(256) NULL,
                \`description\` varchar(4096) NULL,
                \`avatar\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`people\`
            ADD CONSTRAINT \`FK_d5929035bcab537a8e053dcc540\` FOREIGN KEY (\`avatar\`) REFERENCES \`media_assets\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`people\` DROP FOREIGN KEY \`FK_d5929035bcab537a8e053dcc540\`
        `);
    await queryRunner.query(`
            DROP TABLE \`people\`
        `);
  }
}
