import { PERMISSION_TABLE_NAME } from '@/modules/rbac/entities/permission.entity';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPeoplePermission1747296513782 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO ${PERMISSION_TABLE_NAME} (slug, name, description, module, groups, level) VALUES
           ('MANAGE_PEOPLE', 'Manage People', 'Can create, update, view, and delete any people.', 'people', 'people','org'),
           ('VIEW_PEOPLE', 'View People', 'Can view available people.', 'people', 'people','org');`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('<PERSON><PERSON><PERSON>_PEOPLE', 'VIEW_PEOPLE');`);
  }
}
