import { IsS<PERSON>, <PERSON>NotE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Is<PERSON><PERSON>, Matches } from 'class-validator';

export class UpdatePeopleDto {
  @IsString({ message: 'Name must be a string.' })
  @IsNotEmpty({ message: 'Name is required.' })
  name: string;

  @IsOptional()
  @IsEmail({}, { message: 'Email must be a valid email address.' })
  email?: string;

  @IsOptional()
  @Matches(/^\+?\d{10,15}$/, { message: 'Mobile number must be valid (10-15 digits, can start with +).' })
  mobileNo?: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string.' })
  description?: string;

  @IsOptional()
  @IsString({ message: 'Avatar must be a string.' })
  avatar?: string;
}
