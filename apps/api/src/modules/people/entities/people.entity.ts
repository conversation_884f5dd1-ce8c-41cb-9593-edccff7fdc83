import { MediaAsset } from '@/modules/media-asset/entities/media-asset.entity';
import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';

export const PEOPLE_TABLE_NAME = 'people';

@Entity({ name: PEOPLE_TABLE_NAME })
export class People {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'avatar' })
  avatar: MediaAsset;

  @Column({ type: 'varchar', length: 256 })
  name: string;

  @Column({ type: 'varchar', length: 4096, nullable: true })
  description: string;
}
