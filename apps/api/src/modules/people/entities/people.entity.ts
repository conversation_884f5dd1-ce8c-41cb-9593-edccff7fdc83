import { MediaAsset } from '@/modules/media-asset/entities/media-asset.entity';
import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';

export const PEOPLE_TABLE_NAME = 'people';

//Here We need to Check In Every Group The People Name Should Be Unique

@Entity({ name: PEOPLE_TABLE_NAME })
export class People {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'avatar' })
  avatar: MediaAsset;

  @Column({ type: 'varchar', length: 256 })
  name: string;

  @Column({ name: 'mobile_no', type: 'varchar', length: 256, nullable: true })
  mobileNo: string;

  @Column({ name: 'email', type: 'varchar', length: 256, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 4096, nullable: true })
  description: string;
}
