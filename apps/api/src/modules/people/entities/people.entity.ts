import { MediaAsset } from '@api/modules/media-asset/entities/media-asset.entity';
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';

export const PEOPLE_TABLE_NAME = 'people';

//Here We need to Check In Every Group The People Name Should Be Unique

@Entity({ name: PEOPLE_TABLE_NAME })
export class People {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => MediaAsset, { nullable: true })
  @JoinColumn({ name: 'avatar' })
  avatar: MediaAsset;

  @Column({ type: 'varchar', length: 256 })
  name: string;

  @Column({ type: 'varchar', length: 64, unique: true })
  slug: string;

  @Column({ name: 'mobile_no', type: 'varchar', length: 16, nullable: true })
  mobileNo: string;

  @Column({ name: 'email', type: 'varchar', length: 64, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  description: string;
}
