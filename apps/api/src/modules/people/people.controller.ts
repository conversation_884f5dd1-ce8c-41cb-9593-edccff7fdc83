import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PeopleService } from './people.service';
import { CreatePeopleDto } from './dto/create-people.dto';
import { UpdatePeopleDto } from './dto/update-people.dto';
import { PeopleListDto } from './dto/people-list.dto';
import { ApiCookieAuth, ApiTags } from '@nestjs/swagger';
import { AccessGuard } from '../auth/guards/access.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PeopleListSwaggerDocs } from './api-docs/people';
import { Permissions } from '../auth/decorators/permission.decorator';
import { PeopleCreateSwaggerDocs } from './api-docs/create-people';
import { PeopleDetailsSwaggerDocs } from './api-docs/people-details';
import { PeopleUpdateSwaggerDocs } from './api-docs/update-people';
import { PeopleDeleteSwaggerDocs } from './api-docs/delete-people';
import { ApiResponseType } from '@/types/api-response';

@ApiTags('People Management')
@Controller('peoples')
export class PeopleController {
  constructor(private readonly peopleService: PeopleService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_PEOPLE')
  @ApiCookieAuth()
  @PeopleListSwaggerDocs()
  @Get()
  async list(@Query() query: PeopleListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.peopleService.getPeopleList(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Data retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_PEOPLE')
  @ApiCookieAuth()
  @PeopleDetailsSwaggerDocs()
  @Get(':peopleId')
  async getDetails(@Param('peopleId') peopleId: string): Promise<ApiResponseType<any>> {
    const people = await this.peopleService.getPeopleById(+peopleId);
    if (!people) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Data retrieved successfully',
      data: people,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_PEOPLE')
  @ApiCookieAuth()
  @PeopleCreateSwaggerDocs()
  @Post()
  async create(@Body() createPeopleDto: CreatePeopleDto): Promise<ApiResponseType<any>> {
    console.log(createPeopleDto);
    //Validate if the provided name is unique for the Group
    const validateName = await this.peopleService.validateName(createPeopleDto.name);
    if (validateName) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Name already exists for the selected group',
          data: null,
        },
        HttpStatus.CONFLICT,
      );
    }

    // If the name is unique, proceed with the creation
    const newPeople = await this.peopleService.createPeople(createPeopleDto);
    if (!newPeople) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Failed to create people',
          data: null,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return {
      status: 'success',
      message: 'People created successfully',
      data: newPeople,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @ApiCookieAuth()
  @Permissions('MANAGE_PEOPLE')
  @PeopleUpdateSwaggerDocs()
  @Put(':peopleId')
  async update(
    @Param('peopleId') peopleId: string,
    @Body() updatePeopleDto: UpdatePeopleDto,
  ): Promise<ApiResponseType<any>> {
    // Validate if the provided name is unique for the Group
    const validateName = await this.peopleService.validateName(updatePeopleDto.name, peopleId);
    if (validateName) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Name already exists for the selected group',
          data: null,
        },
        HttpStatus.CONFLICT,
      );
    }
    // If the name is unique, proceed with the update
    const updatedPeople = await this.peopleService.updatePeople(+peopleId, updatePeopleDto);
    if (!updatedPeople) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Failed to update people',
          data: null,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return {
      status: 'success',
      message: 'People updated successfully',
      data: updatedPeople,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @ApiCookieAuth()
  @Permissions('MANAGE_PEOPLE')
  @PeopleDeleteSwaggerDocs()
  @Delete(':peopleId')
  async remove(@Param('peopleId') peopleId: string): Promise<ApiResponseType<any>> {
    const deletedPeople = await this.peopleService.removePeople(+peopleId);
    if (!deletedPeople) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Failed to delete people',
          data: null,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return {
      status: 'success',
      message: 'People deleted successfully',
      data: null,
    };
  }
}
