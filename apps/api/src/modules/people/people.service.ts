import { Injectable } from '@nestjs/common';
import { People } from './entities/people.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { CreatePeopleDto } from './dto/create-people.dto';
import { MediaAssetService } from '../media-asset/media-asset.service';

@Injectable()
export class PeopleService {
  constructor(
    @InjectRepository(People) private peopleRepository: Repository<People>,
    private readonly mediaService: MediaAssetService,
  ) {}

  async getPeopleList(
    keyword: string | null = null,
    page: number,
    limit: number,
  ): Promise<{
    data: People[];
    total: number;
    page: number;
    limit: number;
  }> {
    console.log(page);
    const query = this.peopleRepository.createQueryBuilder('people');
    if (keyword) {
      query.andWhere('people.name LIKE :keyword', { keyword: `%${keyword}%` });
    }
    query
      .orderBy('people.name', 'ASC')
      .skip((page - 1) * limit)
      .take(limit);
    const [data, total] = await query.getManyAndCount();
    return { data, total, page, limit };
  }

  async getPeopleById(peopleId: number): Promise<People | null> {
    return this.peopleRepository.findOne({ where: { id: peopleId }, relations: ['avatar'] });
  }

  async validateName(name: string, peopleId?: string): Promise<boolean> {
    const where: Record<string, any> = { name };
    if (peopleId !== undefined) {
      where.id = Not(Number(peopleId));
    }
    const people = await this.peopleRepository.findOne({ where });
    return !!people;
  }

  async createPeople(people: CreatePeopleDto): Promise<People> {
    // if people has an avatar, set it to null
    let peopleAvatar = undefined;
    if (people.avatar) {
      peopleAvatar = await this.mediaService.getMediaAssetById(Number(people.avatar));
    }
    const newPeople = this.peopleRepository.create({
      name: people.name,
      description: people.description,
      email: people.email ?? undefined,
      mobileNo: people.mobileNo ?? undefined,
      avatar: peopleAvatar ?? undefined,
    });
    return this.peopleRepository.save(newPeople);
  }

  async updatePeople(peopleId: number, people: CreatePeopleDto): Promise<People | null> {
    const existingPeople = await this.peopleRepository.findOneBy({ id: peopleId });
    if (!existingPeople) {
      return null;
    }

    let peopleAvatar = undefined;
    if (people.avatar) {
      peopleAvatar = await this.mediaService.getMediaAssetById(Number(people.avatar));
    }

    const updatedPeople = this.peopleRepository.merge(existingPeople, {
      name: people.name,
      description: people.description,
      email: people.email ?? undefined,
      mobileNo: people.mobileNo ?? undefined,
      avatar: peopleAvatar ?? undefined,
    });

    return this.peopleRepository.save(updatedPeople);
  }

  async removePeople(peopleId: number): Promise<People | null> {
    const people = await this.peopleRepository.findOneBy({ id: peopleId });
    if (!people) {
      return null;
    }
    return this.peopleRepository.remove(people);
  }
}
