import { Module } from '@nestjs/common';
import { PeopleService } from './people.service';
import { PeopleController } from './people.controller';
import { People } from './entities/people.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaAssetModuleModule } from '../media-asset/media-asset.module';

@Module({
  imports: [MediaAssetModuleModule, TypeOrmModule.forFeature([People])],
  controllers: [PeopleController],
  providers: [PeopleService],
  exports: [PeopleService],
})
export class PeopleModule {}
