import { applyDecorators } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';

export function PeopleUpdateSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Update a person' }),
    ApiBody({
      description: 'Person update payload',
      schema: {
        type: 'object',
        required: ['id', 'name', 'group'],
        properties: {
          id: {
            type: 'number',
            example: 1,
            description: 'ID of the person to be updated',
          },
          name: {
            type: 'string',
            example: '<PERSON>',
            description: 'Name of the person',
          },
          email: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email address of the person',
          },
          mobileNo: {
            type: 'string',
            example: '+1234567890',
            description: 'Mobile number of the person',
          },
          description: {
            type: 'string',
            example: 'A brief description about the person',
            description: 'Description of the person',
          },
          avatar: {
            type: 'string',
            example: '1',
            description: "Asset ID of the person's avatar",
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Person updated successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Person updated successfully',
          data: {
            id: '<number>',
            name: '<string>',
            group: '<AUTHOR|EDITOR|TRANSLATOR>',
            description: '<string>',
            email: '<string>',
            mobileNo: '<string>',
            avatar: {
              id: '<number>',
              url: '<string>',
            },
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Validation failed for the input data',
      schema: {
        example: {
          statusCode: 400,
          error: 'Bad Request',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict error, e.g., duplicate name in the same group',
      schema: {
        example: {
          statusCode: 409,
          error: 'Conflict',
          message: 'Name already exists in the specified group.',
        },
      },
    }),
  );
}
