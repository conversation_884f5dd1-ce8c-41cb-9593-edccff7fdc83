import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';

export function PeopleDeleteSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete a person' }),
    ApiParam({
      name: 'id',
      description: 'ID of the person to be deleted',
      type: 'number',
      required: true,
    }),
    ApiResponse({
      status: 200,
      description: 'Person deleted successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Person deleted successfully',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Person not found.',
      schema: {
        example: {
          status: 'error',
          message: 'No Data found',
          data: null,
        },
      },
    }),
  );
}
