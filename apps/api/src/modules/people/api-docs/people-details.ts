import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';

export function PeopleDetailsSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get people details by id' }),
    ApiParam({
      name: 'peopleId',
      required: true,
      description: 'ID of the people',
      type: 'number',
    }),
    ApiResponse({
      status: 200,
      description: 'People details retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'People details retrieved successfully',
          data: {
            id: '<number>',
            name: '<string>',
            description: '<string>',
            email: '<string>',
            mobileNo: '<string>',
            avatar: {
              id: '<number>',
              url: '<string>',
            },
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'People not found',
      schema: {
        example: {
          status: 'error',
          message: 'People not found',
          data: null,
        },
      },
    }),
  );
}
