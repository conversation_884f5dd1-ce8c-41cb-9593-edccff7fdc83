import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';

export function PeopleListSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get people list' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to search for people',
      type: 'string',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      type: 'number',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page',
      type: 'number',
      example: 10,
    }),
    ApiResponse({
      status: 200,
      description: 'People list retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'People list retrieved successfully',
          data: {
            data: [
              {
                id: '<number>',
                name: '<string>',
                email: '<string>',
                mobileNo: '<string>',
                description: '<string>',
                avatar: '<string>',
              },
            ],
            total: '<number>',
            page: '<number>',
            limit: '<number>',
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request.',
      schema: {
        example: {
          status: 'error',
          message: 'Invalid input data',
        },
      },
    }),
  );
}
