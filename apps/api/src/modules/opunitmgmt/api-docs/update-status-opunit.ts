import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function UpdateStatusOpunitSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Update status of an existing Operational Unit' }),
    ApiResponse({
      status: 200,
      description: 'Operational Unit status updated successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Operational Unit status updated successfully',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Operational Unit not found',
      schema: {
        example: {
          status: 'error',
          message: 'Operational Unit not found',
        },
      },
    }),
  );
}
