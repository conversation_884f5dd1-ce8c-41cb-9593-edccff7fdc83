import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';

export function ListOpunitSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Get list of Operational Units' }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to filter data',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page for pagination',
      example: 10,
    }),
    ApiResponse({
      status: 404,
      description: 'No data found',
      schema: {
        example: {
          status: 'error',
          message: 'No data found',
          data: [],
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Data retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Data retrieved successfully',
          data: {
            items: [
              {
                id: 1,
                name: 'Main Warehouse',
                address: '123 Main Street',
                emailId: '<EMAIL>',
                mobileNo: '1234567890',
                isActive: 1,
                isInfiniteStock: 0,
              },
            ],
            total: 1,
            page: 1,
            limit: 10,
          },
        },
      },
    }),
  );
}
