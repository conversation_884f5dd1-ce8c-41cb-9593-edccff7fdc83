import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function GetOpunitSwaggerDocs() {
  return applyDecorators(
    ApiOperation({ summary: 'Get opunit by ID' }),
    ApiResponse({
      status: 200,
      description: 'Opunit retrieved successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Opunit retrieved successfully',
          data: {
            id: 1,
            name: 'Main Warehouse',
            address: '123 Main Street',
            emailId: '<EMAIL>',
            mobileNo: '1234567890',
            isActive: 1,
            isInfiniteStock: 0,
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'No data found',
      schema: {
        example: {
          status: 'error',
          message: 'No Opunit found',
          data: [],
        },
      },
    }),
  );
}
