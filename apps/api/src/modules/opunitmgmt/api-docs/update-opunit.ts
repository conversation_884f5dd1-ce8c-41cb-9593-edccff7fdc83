import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';

export function UpdateOpunitSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Update an existing Operational Unit' }),
    ApiBody({
      description: 'Payload to update an existing Operational Unit',
      schema: {
        type: 'object',
        required: ['name', 'address', 'emailId', 'mobileNo'],
        properties: {
          name: {
            type: 'string',
            example: 'Operational Unit 1',
            description: 'Name of the operational unit',
          },
          address: {
            type: 'string',
            example: '123 Main Street',
            description: 'Address of the operational unit',
          },
          emailId: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email address of the operational unit',
          },
          mobileNo: {
            type: 'string',
            example: '1234567890',
            description: 'Mobile number of the operational unit',
          },
          isActive: {
            type: 'boolean',
            example: true,
            description: 'Is the operational unit active?',
          },
          isInfiniteStock: {
            type: 'boolean',
            example: false,
            description: 'Is the operational unit infinite stock?',
          },
          proxyOpUnit: {
            type: 'number',
            example: 1,
            description: 'ID of the proxy operational unit (if applicable)',
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Operational Unit updated successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Operational Unit updated successfully',
          data: {
            id: 1,
            name: 'Main Warehouse',
            address: '123 Main Street',
            emailId: '<EMAIL>',
            mobileNo: '1234567890',
            isActive: 1,
            isInfiniteStock: 0,
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Operational Unit not found',
      schema: {
        example: {
          status: 'error',
          message: 'Operational Unit not found',
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Validation failed for the input data',
      schema: {
        example: {
          statusCode: 400,
          error: 'Bad Request',
          message: [
            {
              property: 'name',
              errors: ['Name cannot be empty.'],
            },
          ],
        },
      },
    }),
    ApiResponse({
      status: 409,
      description: 'Conflict: Opunit name already exists',
      schema: {
        example: {
          status: 'error',
          message: 'Opunit name already exists',
        },
      },
    }),
  );
}
