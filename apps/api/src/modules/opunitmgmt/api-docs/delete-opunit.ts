import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function DeleteOpunitSwaggerDoc() {
  return applyDecorators(
    ApiOperation({ summary: 'Delete an existing Operational Unit' }),
    ApiResponse({
      status: 200,
      description: 'Operational Unit deleted successfully',
      schema: {
        example: {
          status: 'success',
          message: 'Operational Unit deleted successfully',
          data: null,
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: 'Operational Unit not found',
      schema: {
        example: {
          status: 'error',
          message: 'Operational Unit not found',
        },
      },
    }),
  );
}
