import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

export const OPUNIT_TABLE_NAME = 'opunit';

@Entity({ name: OPUNIT_TABLE_NAME })
export class Opunit {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 256 })
  name: string;

  @Column({ type: 'varchar', length: 1024, nullable: true })
  address: string;

  @Column({ name: 'email_id', type: 'varchar', length: 256, nullable: true })
  emailId: string;

  @Column({ type: 'varchar', length: 16, nullable: true })
  mobileNo: string;

  @Column({ name: 'is_active', type: 'tinyint', default: 1 })
  isActive: boolean;

  /**
   * When this is true then the stock moved to this opunit
   * will be considered as sale
   */
  @Column({ name: 'is_infinite_stock', type: 'tinyint', default: 0 })
  isInfiniteStock: boolean;

  /**
   * When isInfiniteStock is true then any stock transfer order
   * from this opunit will  go via the proxyOpunit.
   * e.g. we have a opunit as below:
   * ShopA{
   *  isInfiniteStock: true,
   *  proxyOpUnit: mainWareHouse
   * }
   * When a stock transfer executed from ShopA to another opunit, say, ShopB Then
   *  - One Stock transfer will be done between ShopA and mainWareHouse
   *  - Another one will be done from mainWareHouse to ShopB
   *
   */
  @ManyToOne(() => Opunit)
  @JoinColumn({ name: 'proxy_opunit' })
  proxyOpUnit: Opunit;
}
