import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { Opunit } from './entities/operationalunit.entity';
import { CreateOpunitDto } from './dto/create-opunit.dto';
import { UpdateOpunitDto } from './dto/update-opunit.dto';

@Injectable()
export class OpunitmgmtService {
  constructor(
    @InjectRepository(Opunit)
    private readonly opunitRepository: Repository<Opunit>,
  ) {}

  async getOpunitList(
    keyword: string | null = null,
    page: number,
    limit: number,
  ): Promise<{
    data: Opunit[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.opunitRepository.findAndCount({
      where: keyword ? { name: Like(`%${keyword}%`) } : undefined,
      skip: (page - 1) * limit,
      take: limit,
      order: { name: 'ASC' },
    });
    return { data, total, page, limit };
  }

  async getOpunitById(opunitId: number): Promise<Opunit | null> {
    return await this.opunitRepository.findOne({
      where: { id: opunitId },
      relations: ['proxyOpUnit'],
    });
  }

  async getOpunitByName(name: string): Promise<Opunit | null> {
    return await this.opunitRepository.findOne({
      where: { name },
    });
  }

  async createOpunit(opunitData: CreateOpunitDto): Promise<Opunit> {
    // If proxyOpUnit is provided as an ID, fetch the corresponding Opunit entity
    let proxyOpUnitEntity: Opunit | null = null;
    if (opunitData.proxyOpUnit) {
      proxyOpUnitEntity = await this.opunitRepository.findOne({
        where: { id: opunitData.proxyOpUnit },
      });
    }

    // Create a new instance of the Opunit entity
    const opunit = this.opunitRepository.create({
      ...opunitData,
      proxyOpUnit: proxyOpUnitEntity === null ? undefined : proxyOpUnitEntity, // Ensure compatibility with the entity definition
    });

    // Save the new operational unit to the database
    return await this.opunitRepository.save(opunit);
  }

  async updateOpunit(opunitId: number, opunitData: UpdateOpunitDto): Promise<Opunit> {
    // Fetch the existing Opunit entity
    const opunit = await this.opunitRepository.findOne({
      where: { id: opunitId },
    });

    if (!opunit) {
      throw new Error('Operational Unit not found');
    }

    // If proxyOpUnit is provided as an ID, fetch the corresponding Opunit entity
    let proxyOpUnitEntity: Opunit | null = null;
    if (opunitData.proxyOpUnit) {
      proxyOpUnitEntity = await this.opunitRepository.findOne({
        where: { id: opunitData.proxyOpUnit },
      });
    }

    // Update the existing Opunit entity with the provided data
    opunit.name = opunitData.name;
    opunit.address = opunitData.address ? opunitData.address : '';
    opunit.emailId = opunitData.emailId ? opunitData.emailId : '';
    opunit.mobileNo = opunitData.mobileNo ? opunitData.mobileNo : '';
    opunit.isInfiniteStock = opunitData.isInfiniteStock ? opunitData.isInfiniteStock : false;
    if (proxyOpUnitEntity !== null) {
      opunit.proxyOpUnit = proxyOpUnitEntity;
    }
    // Save the updated Opunit entity to the database
    return await this.opunitRepository.save(opunit);
  }

  async deleteOpunit(opunitId: number): Promise<void> {
    const opunit = await this.opunitRepository.findOne({
      where: { id: opunitId },
    });
    if (!opunit) {
      throw new Error('Operational Unit not found');
    }
    await this.opunitRepository.remove(opunit);
  }

  async updateOpunitStatus(opunitId: number): Promise<Opunit | null> {
    const opunit = await this.opunitRepository.findOne({
      where: { id: opunitId },
    });
    if (!opunit) {
      throw new Error('Operational Unit not found');
    }
    opunit.isActive = !opunit.isActive;
    return await this.opunitRepository.save(opunit);
  }
}
