import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { OpunitmgmtService } from './opunitmgmt.service';
import { OpunitListDto } from './dto/opunit-list.dto';
import { ApiResponseType } from 'src/types/api-response';
import { ApiCookieAuth, ApiTags } from '@nestjs/swagger';
import { CreateOpunitDto } from './dto/create-opunit.dto';
import { UpdateOpunitDto } from './dto/update-opunit.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AccessGuard } from '../auth/guards/access.guard';
import { Permissions } from '../auth/decorators/permission.decorator';
import { ListOpunitSwaggerDoc } from './api-docs/list-opunit';
import { GetOpunitSwaggerDocs } from './api-docs/show-opunit';
import { CreateOpunitSwaggerDoc } from './api-docs/create-opunit';
import { UpdateOpunitSwaggerDoc } from './api-docs/update-opunit';
import { DeleteOpunitSwaggerDoc } from './api-docs/delete-opunit';
import { UpdateStatusOpunitSwaggerDoc } from './api-docs/update-status-opunit';

@ApiTags('Operational Unit Management')
@Controller('operational-units')
export class OpunitmgmtController {
  constructor(private readonly opunitmgmtService: OpunitmgmtService) {}

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_OPUNIT')
  @ApiCookieAuth()
  @ListOpunitSwaggerDoc()
  @Get()
  async getOpunitList(@Query() query: OpunitListDto): Promise<ApiResponseType<any>> {
    const { keyword, page, limit } = query;
    const { data, total } = await this.opunitmgmtService.getOpunitList(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Opunits found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      status: 'success',
      message: 'Opunits retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('VIEW_OPUNIT')
  @ApiCookieAuth()
  @GetOpunitSwaggerDocs()
  @Get('/:opunitId')
  async getOpunitById(@Param('opunitId') opunitId: number): Promise<ApiResponseType<any>> {
    const opunit = await this.opunitmgmtService.getOpunitById(opunitId);
    if (!opunit) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Opunit found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Opunit retrieved successfully',
      data: opunit,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_OPUNIT')
  @ApiCookieAuth()
  @CreateOpunitSwaggerDoc()
  @Post()
  async createOpunit(@Body() data: CreateOpunitDto) {
    const existing = await this.opunitmgmtService.getOpunitByName(data.name);
    if (existing) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Opunit name already exists',
        },
        HttpStatus.CONFLICT,
      );
    }
    console.log(data);

    const opunit = await this.opunitmgmtService.createOpunit(data);
    return {
      status: 'success',
      message: 'Operational Unit created successfully',
      data: opunit,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_OPUNIT')
  @ApiCookieAuth()
  @UpdateOpunitSwaggerDoc()
  @Put('/:opunitId')
  async updateOpunit(@Param('opunitId') opunitId: number, @Body() data: UpdateOpunitDto) {
    // Check if the Operational Unit exists
    const existingOpunit = await this.opunitmgmtService.getOpunitById(opunitId);
    if (!existingOpunit) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Operational Unit not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const existing = await this.opunitmgmtService.getOpunitByName(data.name);
    if (!existing || existing.id !== opunitId) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Operational Unit not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const opunit = await this.opunitmgmtService.updateOpunit(opunitId, data);
    return {
      status: 'success',
      message: 'Operational Unit updated successfully',
      data: opunit,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_OPUNIT')
  @ApiCookieAuth()
  @DeleteOpunitSwaggerDoc()
  @Delete('/:opunitId')
  async deleteOpunit(@Param('opunitId') opunitId: number) {
    // Check if the Operational Unit exists
    const existingOpunit = await this.opunitmgmtService.getOpunitById(opunitId);
    if (!existingOpunit) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Operational Unit not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.opunitmgmtService.deleteOpunit(opunitId);
    return {
      status: 'success',
      message: 'Operational Unit deleted successfully',
      data: null,
    };
  }

  @UseGuards(JwtAuthGuard, AccessGuard)
  @Permissions('MANAGE_OPUNIT')
  @ApiCookieAuth()
  @UpdateStatusOpunitSwaggerDoc()
  @Put('/:id/update-status')
  async updateOpunitStatus(@Param('id') id: number) {
    // Check if the Operational Unit exists
    const existingOpunit = await this.opunitmgmtService.getOpunitById(id);
    if (!existingOpunit) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Operational Unit not found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    await this.opunitmgmtService.updateOpunitStatus(id);
    return {
      status: 'success',
      message: 'Operational Unit status updated successfully',
      data: null,
    };
  }
}
