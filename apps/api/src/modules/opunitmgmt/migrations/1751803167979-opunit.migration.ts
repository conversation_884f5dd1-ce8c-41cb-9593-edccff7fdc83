import { MigrationInterface, QueryRunner } from 'typeorm';
import { OPUNIT_TABLE_NAME } from '../entities/operationalunit.entity';

export class Opunit_1751803167979 implements MigrationInterface {
  name = 'Opunit_1751803167979';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`${OPUNIT_TABLE_NAME}\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(256) NOT NULL,
                \`address\` varchar(1024) NULL,
                \`email_id\` varchar(256) NULL,
                \`mobileNo\` varchar(16) NULL,
                \`is_active\` tinyint NOT NULL DEFAULT '1',
                \`is_infinite_stock\` tinyint NOT NULL DEFAULT '0',
                \`proxy_opunit\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
    await queryRunner.query(`
            ALTER TABLE \`${OPUNIT_TABLE_NAME}\`
            ADD CONSTRAINT \`FK_cf815ad5a7e7a7fb33b86f07fb2\` FOREIGN KEY (\`proxy_opunit\`) REFERENCES \`${OPUNIT_TABLE_NAME}\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`${OPUNIT_TABLE_NAME}\` DROP FOREIGN KEY \`FK_cf815ad5a7e7a7fb33b86f07fb2\`
        `);
    await queryRunner.query(`
            DROP TABLE \`${OPUNIT_TABLE_NAME}\`
        `);
  }
}
