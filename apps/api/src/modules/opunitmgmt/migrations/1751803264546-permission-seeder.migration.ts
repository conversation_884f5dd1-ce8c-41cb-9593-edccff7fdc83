import { MigrationInterface, QueryRunner } from 'typeorm';
import { PERMISSION_TABLE_NAME } from '../../rbac/entities/permission.entity';

export class OpunitPermissionSeeder_1751803264546 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO ${PERMISSION_TABLE_NAME} (slug, name, description, module, \`groups\`, level) VALUES
               ('MANAGE_OPUNIT', 'Manage Opunit', 'Can create, update, view, and delete any Operational Unit.', 'opunitmgmt', '','org'),
               ('VIEW_OPUNIT', 'View Opunit', 'Can view available Operational Units.', 'opunitmgmt', '','org');`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DELETE FROM ${PERMISSION_TABLE_NAME} WHERE slug IN ('<PERSON><PERSON>GE_OPUNIT', 'VIEW_OPUNIT');
      `);
  }
}
