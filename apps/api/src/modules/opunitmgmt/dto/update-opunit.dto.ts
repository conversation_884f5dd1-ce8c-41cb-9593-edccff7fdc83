import { IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty, ValidateIf, IsNumber } from 'class-validator';

export class UpdateOpunitDto {
  @IsString({ message: 'Name must be a string.' })
  @IsNotEmpty({ message: 'Name cannot be empty.' })
  @MaxLength(1024, { message: 'Name must not exceed 1024 characters.' })
  name: string;

  @IsOptional()
  @IsString({ message: 'Address must be a string.' })
  @MaxLength(2048, { message: 'Address must not exceed 2048 characters.' })
  address?: string;

  @IsOptional()
  @IsString({ message: 'Email ID must be a string.' })
  @MaxLength(1024, { message: 'Email ID must not exceed 1024 characters.' })
  emailId?: string;

  @IsOptional()
  @IsString({ message: 'Mobile number must be a string.' })
  @MaxLength(16, { message: 'Mobile number must not exceed 16 characters.' })
  mobileNo?: string;

  @IsBoolean({ message: 'isActive must be a boolean value.' })
  isActive?: boolean;

  @IsBoolean({ message: 'isInfiniteStock must be a boolean value.' })
  isInfiniteStock?: boolean;

  @ValidateIf((o: UpdateOpunitDto) => o.isInfiniteStock === true) // Explicitly type the parameter
  @IsNotEmpty({
    message: 'proxyOpUnit is required when isInfiniteStock is true.',
  })
  @IsNumber({}, { message: 'proxyOpUnit must be a number.' })
  proxyOpUnit?: number; // Assuming proxyOpUnit will be passed as an ID
}
