import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';

export function ListCountrySwaggerDocs() {
  return applyDecorators(
    ApiOperation({
      summary: 'List all countries',
    }),
    ApiQuery({
      name: 'keyword',
      required: false,
      description: 'Keyword to search for publishers',
      type: 'string',
    }),
    ApiQuery({
      name: 'page',
      required: true,
      description: 'Page number for pagination',
      type: 'number',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: true,
      description: 'Number of items per page',
      type: 'number',
      example: 10,
    }),
    ApiResponse({
      status: 200,
      description: 'Country list retrieved successfully.',
      schema: {
        example: {
          status: 'success',
          message: 'Data retrieved successfully',
          data: {
            items: [
              {
                name: '<string>',
                dial_code: '<string>',
                code: '<string>',
              },
            ],
            total: '<number>',
            page: '<number>',
            limit: '<number>',
          },
        },
      },
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request.',
      schema: {
        example: {
          status: 'error',
          message: 'Bad request.',
          data: null,
        },
      },
    }),
  );
}
