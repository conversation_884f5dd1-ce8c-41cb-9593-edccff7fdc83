import { Injectable } from '@nestjs/common';
import { countries, Country } from '../data/countries';

@Injectable()
export class CountryService {
  getCountryList(
    keyword: string,
    page: number,
    limit: number,
  ): {
    data: Country[];
    total: number;
    page: number;
    limit: number;
  } {
    // Filter by name only (case-insensitive)
    const filtered = countries.filter((country: Country) => {
      if (!keyword) return true;
      return country.name.toLowerCase().includes(keyword.toLowerCase());
    });

    // Pagination
    const total = filtered.length;
    const start = (page - 1) * limit;
    const end = start + limit;
    const data: Country[] = filtered.slice(start, end);

    return {
      data,
      total,
      page,
      limit,
    };
  }
}
