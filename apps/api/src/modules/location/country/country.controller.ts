import { Controller, Get, HttpException, HttpStatus, Query } from '@nestjs/common';
import { CountryService } from './country.service';
import { CountryListDto } from '../dto/country-list.dto';
import { ListCountrySwaggerDocs } from '../api-docs/list-country';

@Controller('country')
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @Get()
  @ListCountrySwaggerDocs()
  getAllCountries(@Query() query: CountryListDto) {
    const { keyword, page, limit } = query;
    const { data, total } = this.countryService.getCountryList(keyword, page, limit);

    if (!data || data.length === 0) {
      throw new HttpException(
        {
          status: 'error',
          message: 'No Data found',
          data: [],
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      status: 'success',
      message: 'Data retrieved successfully',
      data: {
        items: data,
        total,
        page,
        limit,
      },
    };
  }
}
