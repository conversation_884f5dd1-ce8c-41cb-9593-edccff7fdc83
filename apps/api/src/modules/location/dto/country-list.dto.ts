import { Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';

export class CountryListDto {
  @IsOptional()
  @IsString({ message: 'Keyword must be a string' })
  keyword: string;

  @IsOptional()
  @Type(() => Number) // Converts string to number
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page: number;

  @IsOptional()
  @Type(() => Number) // Converts string to number
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  limit: number;
}
