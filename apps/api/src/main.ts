/* eslint-disable @typescript-eslint/no-floating-promises */
import '@/configs/loadconfig';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { BadRequestException, ValidationError, ValidationPipe } from '@nestjs/common';
import * as cookieParser from 'cookie-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      exceptionFactory: (validationErrors: ValidationError[] = []) => {
        console.log(validationErrors);
        const formattedErrors = validationErrors.map((error) => {
          return {
            property: error.property,
            errors: Object.values(error.constraints || {}),
          };
        });
        return new BadRequestException({
          statusCode: 400,
          error: 'Bad Request',
          message: formattedErrors,
        });
      },
    }),
  );

  app.use(cookieParser());
  app.enableCors();

  const config = new DocumentBuilder()
    .setTitle('Deys ERP API')
    .setDescription('The Deys ERP API description')
    .setVersion('1.0')
    .addTag('deys-erp')
    .addCookieAuth('access_token')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);

  SwaggerModule.setup('api-doc', app, documentFactory);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
