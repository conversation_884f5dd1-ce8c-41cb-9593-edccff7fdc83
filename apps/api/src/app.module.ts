import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { RbacModule } from './modules/rbac/rbac.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsermgmtModule } from './modules/usermgmt/usermgmt.module';
import { OpunitmgmtModule } from './modules/opunitmgmt/opunitmgmt.module';
import { CatalogModule } from './modules/catalog/catalog.module';
import { MediaAssetModuleModule } from './modules/media-asset/media-asset.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { AppConfig } from './configs/app-config';
import { PeopleModule } from './modules/people/people.module';
import { PublishersModule } from './modules/publishers/publishers.module';
import { LocationModule } from './modules/location/location.module';

const dbModule = TypeOrmModule.forRoot({
  type: 'mariadb',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? Number(process.env.DB_PORT) : 3306,
  username: process.env.DB_USER_NAME || 'dyspxn-erp-db-user',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'dyspxn-erp-db',
  synchronize: false,
  entities: [__dirname + '/**/*.entity{.ts,.js}'],
});
const staticModule = ServeStaticModule.forRoot({
  rootPath: AppConfig.getEnvPath('PUBLIC_DIR'),
});

@Module({
  imports: [
    staticModule,
    dbModule,
    AuthModule,
    RbacModule,
    UsermgmtModule,
    OpunitmgmtModule,
    CatalogModule,
    MediaAssetModuleModule,
    PeopleModule,
    PublishersModule,
    LocationModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
