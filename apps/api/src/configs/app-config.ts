import * as path from 'path';

export type EnvConfig = {
  PORT: number;
  DB_HOST: string;
  DB_PORT: string;
  DB_USER_NAME: string;
  DB_PASSWORD: string;
  DB_NAME: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  MEDIA_ASSET_FILE_UPLOAD_DIR: string;
  MEDIA_ASSET_FILE_SIZE_LIMIT_IN_BYTES: number;
  PUBLIC_DIR: string;
};

export class AppConfig {
  static get(key: keyof EnvConfig, fallbackValue?: string): string {
    const value = process.env[key];
    if (value !== undefined) {
      return value;
    }
    if (fallbackValue !== undefined) {
      return fallbackValue;
    }
    throw new Error(`Config value for "${key}" not found`);
  }
  static getNumber(key: keyof EnvConfig, fallbackValue?: number): number {
    const value = process.env[key];
    if (value !== undefined) {
      return Number(value);
    }
    if (fallbackValue !== undefined) {
      return fallbackValue;
    }
    throw new Error(`Config value for "${key}" not found`);
  }

  /**
   * This will resolve anyPath relative to current process root
   * if the path is absolutePath then it will be returned as is
   */
  static getEnvPath(key: keyof EnvConfig, fallbackRelativePath?: string): string {
    const envValue = process.env[key];

    if (envValue) {
      if (path.isAbsolute(envValue)) {
        return envValue;
      }

      return path.resolve(process.cwd(), envValue);
    }

    if (fallbackRelativePath) {
      return path.resolve(process.cwd(), fallbackRelativePath);
    }

    throw new Error(`Config value for "${key}" not found`);
  }
}
