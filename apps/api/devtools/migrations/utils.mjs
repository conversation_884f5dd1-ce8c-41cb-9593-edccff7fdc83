import { readdir } from 'fs/promises';
import { join } from 'path';

async function getSubfolders(folderPath) {
  const entries = await readdir(folderPath, { withFileTypes: true });

  const folders = entries.filter((entry) => entry.isDirectory()).map((entry) => entry.name);

  return folders;
}
export const getAvailableModules = async () => getSubfolders(join(__dirname, '../../src/modules'));

export const getAllEntityFiles = async (moduleName) => {
  try {
    const entityPath = join(__dirname, '../../src/modules', moduleName, 'entities');
    const entries = await readdir(entityPath, { withFileTypes: true, recursive: true });
    const entityFiles = entries
      .filter((entry) => entry.isFile() && entry.name.endsWith('.entity.ts'))
      .map((entry) => ({ name: entry.name, parentPath: entry.parentPath }));
    return entityFiles;
  } catch (error) {
    return [];
  }
};
