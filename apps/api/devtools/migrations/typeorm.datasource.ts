import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as fs from 'fs';

dotenv.config({ path: '.env' });

if (fs.existsSync('.env.local')) {
  dotenv.config({ path: '.env.local', override: true });
}

const entities: string | undefined = process.env.ENTITIES;

const getEntities = () => {
  if (entities) {
    return JSON.parse(entities) as string[];
  }
  return [__dirname + `/../../src/**/*.entity{.ts,.js}`];
};

export default new DataSource({
  type: 'mariadb',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? Number(process.env.DB_PORT) : 8306,
  username: process.env.DB_USER_NAME || 'dyspxn-erp-db-user',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'dyspxn-erp-db',
  entities: getEntities(),
  migrations: [__dirname + `/../../src/modules/*/migrations/*{.ts,.js}`],
});
