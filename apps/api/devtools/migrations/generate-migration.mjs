#!/usr/bin/env zx

import { $, chalk } from 'zx';
import { join } from 'path';
import enquirer from 'enquirer';
import { getAvailableModules, getAllEntityFiles } from './utils.mjs';

const availableModules = await getAvailableModules();

const selectedEntities = [];
const selectedModules = await new enquirer.MultiSelect({
  message: 'Select modules:',
  choices: availableModules,
}).run();

const selectIndividualEntity = await new enquirer.Confirm({
  message: 'Select individual entity?',
  initial: false,
}).run();

if (selectIndividualEntity) {
  while (true) {
    const individualEntities = await new enquirer.MultiSelect({
      message: 'Select entities:',
      choices: (await Promise.all(selectedModules.map(getAllEntityFiles))).flat(),
      result() {
        return this.selected.map((entity) => `${entity.parentPath}/${entity.name}`);
      },
    }).run();
    if (individualEntities.length) {
      selectedEntities.push(...individualEntities);
      break;
    }
  }
} else {
  selectedModules.forEach((module) => {
    selectedEntities.push(__dirname + `/../../src/modules/${module}/**/*.entity{.ts,.js}`);
  });
}

console.log(selectedEntities);

const targetModule = await new enquirer.Select({
  message: 'Which module should the migration be generated in?:',
  choices: availableModules,
}).run();

const migrationName = await new enquirer.Input({
  message: 'Enter migration file name:',
}).run();

console.log(chalk.green('- Running any pending migration first'));
await $`yarn migration:run`;

console.log(chalk.green('- Generating migration for selected entities'));
try {
  await $`ENTITIES=${JSON.stringify(selectedEntities)} yarn typeorm migration:generate ./src/modules/${targetModule}/migrations/${migrationName} -d ./devtools/migrations/typeorm.datasource.ts  --pretty`;
  const modulePath = join(__dirname, '../../src/modules/', targetModule).toString();
  await $`npx prettier --write ${modulePath}/migrations/*.*`;
  console.log(chalk.green('- Migration generation complete for ' + targetModule));
} catch (error) {
  const err = error;
  const errorMessage = err?.stdout || err?.stderr || err?.message || String(err);
  if (errorMessage.indexOf('No changes in database schema were found') !== -1) {
    console.log(chalk.yellow('No changes in database schema were found - cannot generate a migration.'));
  } else {
    console.error(' Error occurred:', errorMessage);
  }
}
