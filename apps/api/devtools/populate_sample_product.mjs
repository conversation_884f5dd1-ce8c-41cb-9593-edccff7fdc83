/**
 * This will populate some product from deyspublishing.com
 */
import { DataSource } from 'typeorm';

const AppDataSource = new DataSource({
  type: 'mysql',
  host: 'localhost',
  port: 8306,
  username: 'dyspxn-erp-db-user',
  password: 'password',
  database: 'dyspxn-erp-db',
});

const escapse = (str) => str.replace(/'/g, "\\'");

const mapSoureceProduct = (sourceproduct, productTypeId) => {
  const authors = sourceproduct.authors.map((author) => ({
    name: escapse(author.author_name),
    slug: escapse(author.author_code),
  }));
  const editors = sourceproduct.editors.map((editor) => ({
    name: escapse(editor.editor_name),
    slug: escapse(editor.editor_code),
  }));
  const publisher = {
    name: escapse(sourceproduct.publisher.publisher_name),
    slug: escapse(sourceproduct.publisher.publisher_code),
    country: 'India',
  };

  const targetProduct = {
    name: escapse(sourceproduct.title),
    slug: escapse(sourceproduct.book_slug),
    description: '',
    product_type_id: productTypeId,
    attributes: {
      language: 'bengali',
      firstPublishingYear: '',
      lastPublishingYear: '',
      metaTitle: '',
      metaDescription: '',
      author: authors.map((item) => item.slug),
      publisher: publisher.slug,
      editor: editors.map((item) => item.slug),
      translator: [],
    },
  };

  const targetProductVariations = sourceproduct.book_variants.map((variant) => ({
    name: '',
    skucode: variant.sku,
    base_price: Number(variant.maximum_retail_price),
    attributes: {
      edition_printing_year: '',
      bindingType: variant.type,
      nofOfPages: 100,
      weight: variant.weight,
      dimension: '12 X 45 X 87',
      hsn_sac_code: variant.hsn_code,
    },
  }));
  return { authors, editors, publisher, product: { ...targetProduct, variations: targetProductVariations } };
};

const getBookProductTypeId = async (queryRunner) => {
  const result = await queryRunner.query(`
      SELECT id FROM product_type WHERE name = 'Book' LIMIT 1
    `);

  if (result && result.length > 0) {
    return result[0].id;
  }
  throw new Error('Book product type not found. You may need to run migration first.');
};

const insertPeople = async (peoples, queryRunner) => {
  const peopleValues = peoples.map((people) => `('${people.name}','${people.slug}')`);
  await queryRunner.query(`
            INSERT IGNORE INTO \`people\` (\`name\`, \`slug\`) VALUES ${peopleValues.join(',')}
        `);
};
const insertPublisher = async (publishers, queryRunner) => {
  const peopleValues = publishers.map((pu) => `('${pu.name}','${pu.slug}', 'India')`);
  await queryRunner.query(`
            INSERT IGNORE INTO \`publisher\` (\`name\`, \`slug\`, country) VALUES ${peopleValues.join(',')}
        `);
};

const insertSingleProduct = async (productData, queryRunner) => {
  const insertResult = await queryRunner.query(`
      INSERT IGNORE INTO product (name, slug,description,product_type_id,attributes,catalogs)
      VALUES ('${productData.name}', '${productData.slug}','',${productData.product_type_id}, '${JSON.stringify(productData.attributes)}','{}')
    `);
  const productId = insertResult.insertId;

  const variationValues = productData.variations.map(
    (variation) =>
      `('','${variation.skucode}', '${variation.base_price}',${productId},'${JSON.stringify(variation.attributes)}')`,
  );

  await queryRunner.query(`
      INSERT IGNORE INTO product_variation (name,skucode,base_price,product_id,attributes)
      VALUES ${variationValues.join(',')}
    `);
};

const loadData = async () => {
  const datasource = await AppDataSource.initialize();
  const queryRunner = datasource.createQueryRunner();
  try {
    await queryRunner.connect();
    await queryRunner.startTransaction();
    const allPeople = {};
    const allPublishers = {};
    const allProducts = {};

    const bookProductTypeId = await getBookProductTypeId(queryRunner);
    const sourceBooks = await fetch('https://api.deyspublishing.com/api/books-list?limit=100')
      .then((r) => r.json())
      .then((d) => d.data.data);

    for (const book of sourceBooks) {
      const { authors, editors, publisher, product } = mapSoureceProduct(book, bookProductTypeId);
      authors.forEach((au) => {
        allPeople[au.slug] = au;
      });
      editors.forEach((au) => {
        allPeople[au.slug] = au;
      });
      allPublishers[publisher.slug] = publisher;
      allProducts[product.slug] = product;
    }
    await insertPeople(Object.values(allPeople), queryRunner);
    await insertPublisher(Object.values(allPublishers), queryRunner);
    await Promise.all(Object.values(allProducts).map((p) => insertSingleProduct(p, queryRunner)));
    await queryRunner.commitTransaction();
  } catch (e) {
    await queryRunner.rollbackTransaction();
    throw e;
  } finally {
    queryRunner.release();
  }
};

loadData()
  .then(() => {
    console.log('done');
    process.exit(0);
  })
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
