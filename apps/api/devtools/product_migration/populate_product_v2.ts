/**
 * This will populate some product from deyspublishing.com
 */
import { DataSource, QueryRunner } from 'typeorm';
import { RawBookdata } from './source_schema';

const AppDataSource = new DataSource({
  type: 'mysql',
  host: 'localhost',
  port: 8306,
  username: 'dyspxn-erp-db-user',
  password: 'password',
  database: 'dyspxn-erp-db',
});

const escapeStr = (str: string) => str.replace(/'/g, "\\'");

const mapSoureceProduct = (rawBook: RawBookdata, productTypeId: number) => {
  const targetProduct = {
    name: escapeStr(rawBook.title),
    slug: escapeStr(rawBook.book_slug),
    description: rawBook.summary,
    product_type_id: productTypeId,
    attributes: {
      language: rawBook.language,
      firstPublishingYear: rawBook.first_publishing_year,
      lastPublishingYear: rawBook.last_publishing_year,
      metaTitle: rawBook.book_meta_title,
      metaDescription: rawBook.book_meta_description,
      author: rawBook.authors.map((item) => item.author_code),
      publisher: rawBook.publisher.publisher_code,
      editor: [],
      translator: [],
    },
  };

  const targetProductVariations = rawBook.variants.map((variant) => ({
    name: escapeStr(
      `${rawBook.title}|${rawBook.last_publishing_year || rawBook.first_publishing_year}|${variant.type}`,
    ),
    skucode: variant.sku,
    base_price: Number(variant.maximum_retail_price),
    attributes: {
      edition_printing_year: rawBook.last_publishing_year || rawBook.first_publishing_year,
      bindingType: variant.type,
      nofOfPages: 0,
      weight: Number(variant.weight) * 1000,
      dimension: '12 X 45 X 87',
      hsn_sac_code: variant.hsn_code,
    },
  }));
  return { ...targetProduct, variations: targetProductVariations };
};

const getBookProductTypeId = async (queryRunner: QueryRunner) => {
  const result = await queryRunner.query(`
      SELECT id FROM product_type WHERE name = 'Book' LIMIT 1
    `);

  if (result && result.length > 0) {
    return result[0].id;
  }
  throw new Error('Book product type not found. You may need to run migration first.');
};

const insertMedia = async (
  queryRunner: QueryRunner,
  data: { fileURL: string; thumbnailURL: string; folderName: string },
) => {
  const fileName = data.fileURL.split('/').pop();
  const res = await queryRunner.query(`
    INSERT INTO \`media_assets\` (\`file_name\`, \`file_url\`, \`thumbnail_url\`, \`file_type\`, \`folder_name\`, \`metadata\`) 
    VALUES ('${fileName}', '${data.fileURL}', '${data.thumbnailURL}', 'image', '${data.folderName}', '{}')`);
  return res.insertId;
};

const insertPublisher = async (publisher: RawBookdata['publisher'], queryRunner: QueryRunner) => {
  const res = await queryRunner.query(
    `SELECT count(*) as count FROM \`publisher\` WHERE \`slug\`='${publisher.publisher_code}'`,
  );
  if (Number(res[0].count) === 0) {
    console.log('Publisher: ' + publisher.publisher_name);
    const publisherValues = [
      `'${escapeStr(publisher.publisher_name)}'`,
      `'${publisher.publisher_code}'`,
      `'${publisher.publisher_address}'`,
      `'${publisher.publisher_email}'`,
      `'${publisher.publisher_mobile}'`,
      `'${publisher.publisher_country}'`,
    ];
    if (publisher.publisher_image) {
      const mediaId = await insertMedia(queryRunner, {
        fileURL: publisher.publisher_image,
        thumbnailURL: publisher.publisher_thumbnail || publisher.publisher_image,
        folderName: 'publishers',
      });
      const query = `
            INSERT IGNORE INTO \`publisher\` (\`name\`, \`slug\`,\`address\`, \`email_id\`, \`mobile_no\`, \`country\`, \`logo_id\`) 
            VALUES (${[...publisherValues, mediaId].join(',')})
        `;
      await queryRunner.query(query);
    } else {
      await queryRunner.query(`
            INSERT IGNORE INTO \`publisher\` (\`name\`, \`slug\`,\`address\`, \`email_id\`, \`mobile_no\`, \`country\`) 
            VALUES (${publisherValues.join(',')})
        `);
    }
  }
};
const insertAuthor = async (author: RawBookdata['authors'][0], queryRunner: QueryRunner) => {
  const res = await queryRunner.query(
    `SELECT count(*) as count FROM \`people\` WHERE \`slug\`='${author.author_code}'`,
  );
  if (Number(res[0].count) === 0) {
    console.log('Author: ' + author.author_name);
    await queryRunner.query(`
            INSERT IGNORE INTO \`people\` (\`name\`, \`slug\`, \`mobile_no\`, \`email\`) VALUES (
              '${author.author_name}',
              '${author.author_code}',
              '${author.author_mobile || ''}',
              '${author.author_email || ''}'
            )`);
  }
};
const insertSingleProduct = async (rawBook: RawBookdata, queryRunner: QueryRunner) => {
  const bookProductTypeId = await getBookProductTypeId(queryRunner);
  const productData = mapSoureceProduct(rawBook, bookProductTypeId);

  let mediaId = null;
  const productInsertQuery = `INSERT IGNORE INTO product (name,slug,description,product_type_id,attributes,catalogs,cover_image_id) 
                              VALUES(?,?,?,?,?,?,?)`;
  if (rawBook.book_image) {
    mediaId = await insertMedia(queryRunner, {
      fileURL: rawBook.book_image,
      thumbnailURL: rawBook.book_thumbnail || rawBook.book_image,
      folderName: 'Product Images',
    });
  }
  const insertResult = await queryRunner.query(productInsertQuery, [
    productData.name,
    productData.slug,
    productData.description,
    productData.product_type_id,
    JSON.stringify(productData.attributes),
    '{}',
    mediaId,
  ]);
  const productId = insertResult.insertId;

  const variationValues = productData.variations.map(
    (variation) =>
      `('${variation.name}','${variation.skucode}', '${variation.base_price}',${productId},'${JSON.stringify(variation.attributes)}',${mediaId})`,
  );

  await queryRunner.query(`
      INSERT IGNORE INTO product_variation (name,skucode,base_price,product_id,attributes,cover_image_id)
      VALUES ${variationValues.join(',')}
    `);
  console.log('Product: ' + rawBook.title);
};
const loadData = async () => {
  const datasource = await AppDataSource.initialize();
  const queryRunner = datasource.createQueryRunner();
  try {
    await queryRunner.connect();

    let pageNo = 1;
    while (true) {
      await queryRunner.startTransaction();
      const sourceBooks = await fetch(`https://api-dev.deyspublishing.com/api/erp/get-products?limit=20&page=${pageNo}`)
        .then((r) => r.json())
        .then((d) => d.data.data as RawBookdata[]);

      if (sourceBooks.length === 0) {
        break;
      }
      console.log(`Processing Books. Page: ${pageNo}`);
      for (const book of sourceBooks) {
        await Promise.all(book.authors.map((author) => insertAuthor(author, queryRunner)));
        await insertPublisher(book.publisher, queryRunner);
        await insertSingleProduct(book, queryRunner);
      }
      console.log(`Processing Done for. Page: ${pageNo}`);
      pageNo += 1;
      await queryRunner.commitTransaction();
    }
  } catch (e) {
    await queryRunner.rollbackTransaction();
    throw e;
  } finally {
    queryRunner.release();
  }
};

loadData()
  .then(() => {
    console.log('done');
    process.exit(0);
  })
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
