export interface JSONSchema {
  status: string;
  message: string;
  data: Data;
}

export interface Data {
  current_page: number;
  data: RawBookdata[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url: string;
  path: string;
  per_page: number;
  prev_page_url: null;
  to: number;
  total: number;
}

export interface RawBookdata {
  id: number;
  book_slug: string;
  title: string;
  language: Language;
  first_publishing_year: string;
  last_publishing_year: string;
  summary: string;
  book_image: string;
  book_thumbnail: string;
  isbn_number: string;
  book_pages: number;
  tags: string[];
  status: number;
  book_featured: number;
  recommended: number;
  todays_pick: number;
  best_seller: number;
  recent_release: number;
  special_book: number;
  show_in_home_page: number;
  publisher_id: number;
  created_by: number;
  updated_by: number;
  book_meta_title: string;
  book_meta_description: string;
  created_at: Date;
  updated_at: Date;
  publisher: Publisher;
  categories: DatumCategory[];
  authors: DatumAuthor[];
  editors: any[];
  translators: any[];
  variants: Variant[];
  book_variants: Variant[];
  preview_images: PreviewImage[];
}

export interface DatumAuthor {
  id: number;
  author_code: string;
  author_name: string;
  author_mobile: null;
  author_email: null;
  author_description: null;
  author_image: null;
  author_thumbnail: null;
  birth_month: null;
  birth_date: null;
  birth_year: null;
  birth_day_banner: null;
  author_status: number;
  author_featured: number;
  author_meta_title: string;
  author_meta_description: string;
  tags: null;
  created_at: Date;
  updated_at: Date;
  pivot: AuthorPivot;
}

export interface AuthorPivot {
  book_id: number;
  author_id: number;
}

export interface Variant {
  id: number;
  book_id: number;
  sku: string;
  hsn_code: string;
  gst: string;
  type: BookVariantType;
  maximum_retail_price: string;
  weight: string;
  weight_unit: WeightUnit;
  length: string;
  width: string;
  height: string;
  stock: number;
  status: number;
  discount_type: null;
  discount_value: string;
  created_at: Date;
  updated_at: Date;
  discounted_price: string;
  book_data?: BookData;
}

export interface BookData {
  id: number;
  book_slug: string;
  title: string;
  book_thumbnail: string;
  publisher: null;
  categories: BookDataCategory[];
  authors: BookDataAuthor[];
  editors: any[];
}

export interface BookDataAuthor {
  id: number;
  author_name: string;
  pivot: AuthorPivot;
}

export interface BookDataCategory {
  id: number;
  category_name: string;
  pivot: CategoryPivot;
}

export interface CategoryPivot {
  book_id: number;
  category_id: number;
}

export enum BookVariantType {
  Hardcover = 'Hardcover',
  Paperback = 'Paperback',
}

export enum WeightUnit {
  Kg = 'kg',
}

export interface DatumCategory {
  id: number;
  category_name: string;
  category_slug: string;
  category_description: null | string;
  category_image: null | string;
  category_thumbnail: null | string;
  category_meta_title: null | string;
  category_meta_description: null | string;
  category_status: number;
  category_featured: number;
  parent_id: number | null;
  discount_type: null;
  discount_value: string;
  tags: null;
  created_at: Date;
  updated_at: Date;
  pivot: CategoryPivot;
}

export enum Language {
  Bengali = 'Bengali',
}

export interface PreviewImage {
  id: number;
  book_id: number;
  file: string;
  file_thumbnail: string;
  alt_text: string;
  type: PreviewImageType;
  created_at: Date;
  updated_at: Date;
}

export enum PreviewImageType {
  Image = 'IMAGE',
}

export interface Publisher {
  id: number;
  publisher_code: PublisherCode;
  publisher_name: PublisherName;
  publisher_email: null;
  publisher_mobile: null;
  publisher_about: null;
  publisher_image: string;
  publisher_thumbnail: string;
  publisher_address: null;
  publisher_country: PublisherCountry;
  publisher_meta_title: string;
  publisher_meta_description: string;
  publisher_status: number;
  publisher_featured: number;
  discount_type: DiscountType;
  discount_value: string;
  tags: null;
  created_at: Date;
  updated_at: Date;
}

export enum DiscountType {
  Percentage = 'PERCENTAGE',
}

export enum PublisherCode {
  DeysPublishing = 'deys-publishing',
}

export enum PublisherCountry {
  India = 'India',
}

export enum PublisherName {
  DeySPublishing = 'Dey’s Publishing',
}

export interface Link {
  url: null | string;
  label: string;
  active: boolean;
}
