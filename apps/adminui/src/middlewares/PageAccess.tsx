import Spinner from '@adminui/components/common/Spinner';
import { useGetUserPermissionsByModule } from '@adminui/modules/auth/services/auth-service';
import React, { ReactNode, useMemo } from 'react';
import { Navigate } from 'react-router-dom';

interface Props {
  children: ReactNode;
  module: string;
  permissions: string[];
}

const PageAccess: React.FC<Props> = ({ children, module, permissions }) => {
  const { data: response, isLoading } = useGetUserPermissionsByModule(module);

  const hasAccess = useMemo(() => {
    const myPermissions = response?.data || [];
    return myPermissions.includes('FULL_ACCESS') || permissions.some((p) => myPermissions.includes(p));
  }, [response, permissions]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Spinner size={'sm'} color="blue-500" />
      </div>
    );
  }

  return hasAccess ? children : <Navigate to="/no-access" />;
};

export default PageAccess;
