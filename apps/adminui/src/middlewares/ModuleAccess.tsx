import Spinner from '@adminui/components/common/Spinner';
import { useGetModulesAndGroupsPermission } from '@adminui/modules/auth/services/auth-service';
import React, { ReactNode, useMemo } from 'react';
import { Navigate } from 'react-router-dom';

interface Props {
  children: ReactNode;
  modules: string[];
}

const ModuleAccess: React.FC<Props> = ({ children, modules }) => {
  const { data: response, isLoading: modulesAndGroupsLoading } = useGetModulesAndGroupsPermission();

  const hasAccess = useMemo(() => {
    const myModules = response?.data?.modules || [];
    return myModules.includes('FULL_ACCESS') || modules.some((module) => myModules.includes(module));
  }, [response, modules]);

  if (modulesAndGroupsLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Spinner size={'sm'} color="blue-500" />
      </div>
    );
  }

  return hasAccess ? children : <Navigate to="/no-access" />;
};

export default ModuleAccess;
