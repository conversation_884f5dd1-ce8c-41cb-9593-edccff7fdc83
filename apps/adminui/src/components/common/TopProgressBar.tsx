import React from 'react';
import { useIsMutating } from '@tanstack/react-query';
import useLoader from '@adminui/store/LoaderService';

const TopProgressBar: React.FC = () => {
  const isMutating = useIsMutating();
  const { isLoading } = useLoader();
  if (isMutating == 0 && !isLoading) return null;
  return (
    <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 overflow-hidden z-50">
      <div className="h-full bg-black progress-bar"></div>
    </div>
  );
};

export default TopProgressBar;
