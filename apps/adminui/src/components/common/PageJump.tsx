import React from 'react';
import { Input } from '@adminui/components/ui/input';
import { Button } from '@adminui/components/ui/button';

interface Props {
  pageNumber: number;
  handlePageJump: (page: number) => void;
}

const PageJump: React.FC<Props> = ({ pageNumber, handlePageJump }) => {
  const [activePage, setActivePage] = React.useState(pageNumber);
  return (
    <div className="flex flex-row items-center gap-2">
      <label htmlFor="page-number" className="text-gray-700">
        Go to Page
      </label>
      <Input
        type="text"
        className="w-16 text-center h-8"
        value={activePage}
        onChange={(e) => setActivePage(Number(e.target.value))}
      />
      <Button variant="outline" size={'sm'} onClick={() => handlePageJump(activePage)}>
        Jump
      </Button>
    </div>
  );
};

export default PageJump;
