import React from 'react';
import { Input } from '@adminui/components/ui/input';
import { Search } from 'lucide-react';

interface SearchInputProps {
  value?: string;
  placeholder?: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onEnterPress: () => void;
}

const SearchInput: React.FC<SearchInputProps> = ({ value, placeholder, onChange, onEnterPress }) => {
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      onEnterPress();
    }
  };

  return (
    <div className="search-box w-full relative">
      <Input
        type="text"
        value={value}
        placeholder={placeholder || 'Search...'}
        className="pr-10"
        onChange={onChange}
        onKeyDown={handleKeyDown}
        autoComplete="off"
        name="search-input"
        id="search-input"
      />
      <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
    </div>
  );
};

export default SearchInput;
