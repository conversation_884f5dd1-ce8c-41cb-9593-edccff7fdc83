import React, { type ComponentProps } from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '../ui/form';
import { Input } from '../ui/input';

interface Props extends ComponentProps<'input'> {
  control: Control<any>;
  name: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
}

const TextInput: React.FC<Props> = ({ control, name, placeholder, label, isRequired, ...rest }) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            {label} {isRequired && <span style={{ color: 'red' }}>*</span>}
          </FormLabel>
          <FormControl>
            <Input {...field} type="text" placeholder={placeholder} className="h-10" {...rest} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default TextInput;
