import React from 'react';
import { Control } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';
import { Check, ChevronsUpDown, Loader, Trash } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '../ui/command';
import { cn } from '@adminui/lib/utils';

interface Option {
  label: string;
  value: string;
}
interface Props {
  control: Control<any>;
  name: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  isLoading: boolean;
  options: Option[];
  onSearch: (value: string) => void; // Optional search handler
}

const SearchableSelectInput: React.FC<Props> = ({
  control,
  name,
  placeholder,
  label,
  isRequired,
  isLoading,
  options = [],
  onSearch,
}) => {
  const triggerSearch = (value: string) => {
    onSearch(value);
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            {label} {isRequired && <span style={{ color: 'red' }}>*</span>}
          </FormLabel>
          <FormControl>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  className={cn('w-full justify-between h-10', !field.value && 'text-muted-foreground')}
                >
                  {field.value
                    ? options.find((option) => option.value === field.value)?.label
                    : placeholder || 'Select an option'}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="min-w-[200px] p-0 shadow-lg rounded-md">
                <Command>
                  <div className="relative">
                    <CommandInput
                      placeholder={`Search ${label?.toLowerCase()}...`}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => triggerSearch(e.target.value)}
                      className="pr-10"
                    />
                    {isLoading && <Loader size={16} className="absolute right-2 top-2.5 animate-spin text-blue-500" />}
                  </div>
                  <CommandList>
                    <CommandItem
                      onSelect={() => field.onChange('')}
                      className="text-red-500 hover:bg-red-100 flex items-center justify-center"
                    >
                      <Trash className="mr-2 h-4 w-4" /> Clear Selection
                    </CommandItem>
                    {isLoading && (
                      <div className="flex items-center justify-center py-4">
                        <Loader size={16} className="animate-spin text-blue-500" />
                      </div>
                    )}
                    {options.length === 0 ? (
                      <CommandEmpty className="text-gray-500 text-center py-4">No options found.</CommandEmpty>
                    ) : (
                      <CommandGroup>
                        {options.map((option) => (
                          <CommandItem
                            value={option.label}
                            key={option.value}
                            onSelect={() => {
                              field.onChange(option.value);
                            }}
                            className="flex items-center justify-between"
                          >
                            {option.label}
                            <Check
                              className={cn(
                                'ml-auto',
                                option.value === field.value ? 'opacity-100 text-green-500' : 'opacity-0',
                              )}
                            />
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SearchableSelectInput;
