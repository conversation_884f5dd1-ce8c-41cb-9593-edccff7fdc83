import React from 'react';
import { Control } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Textarea } from '../ui/textarea';
import { cn } from '@adminui/lib/utils';

interface Props {
  control: Control<any>;
  name: string;
  label?: string;
  isRequired?: boolean;
  placeholder?: string;
  className?: string;
}

const TextAreaInput: React.FC<Props> = ({ control, name, label, isRequired, placeholder, className }) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          <FormLabel>
            {label} {isRequired && <span style={{ color: 'red' }}>*</span>}
          </FormLabel>
          <FormControl>
            <Textarea {...field} placeholder={placeholder} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default TextAreaInput;
