import { Button } from '@adminui/components/ui/button';
import { PlusIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

type Props = {
  title: string;
  addLinkButton: {
    label: string;
    linkTo: string;
  };
};
const EmptyList = ({ title, addLinkButton }: Props) => {
  return (
    <div className="w-full border border-gray-200 bg-gray-100 rounded flex flex-col gap-4 items-center justify-center h-72">
      <h2>{title}</h2>
      <Button asChild variant="outline">
        <Link to={addLinkButton.linkTo} className="button">
          <PlusIcon className="size-6" />
          {addLinkButton.label}
        </Link>
      </Button>
    </div>
  );
};

export default EmptyList;
