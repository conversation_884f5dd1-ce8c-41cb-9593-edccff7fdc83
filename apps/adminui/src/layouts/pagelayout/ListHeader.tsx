import { But<PERSON> } from '@adminui/components/ui/button';
import { Link } from 'react-router-dom';
import { PlusIcon } from 'lucide-react';

type Props = {
  title: string;
  addLinkButton?: {
    label: string;
    linkTo: string;
  };
};
const ListHeader = (props: Props) => {
  const { title, addLinkButton } = props;
  return (
    <div className="flex justify-between sticky top-0 bg-white z-10  py-4">
      <div>
        <h1 className="text-xl">{title}</h1>
      </div>
      <div className="flex gap-4">
        {addLinkButton ? (
          <Button asChild>
            <Link to={addLinkButton.linkTo} className="button">
              <PlusIcon className="size-6" />
              {addLinkButton.label}
            </Link>
          </Button>
        ) : null}
      </div>
    </div>
  );
};

export default ListHeader;
