import Spinner from '@adminui/components/common/Spinner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@adminui/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@adminui/components/ui/sidebar';
import { useGetMyOpunits, useSetActiveOpunit } from '@adminui/modules/auth/services/auth-service';
import useAuth from '@adminui/store/AuthService';
import { Check, ChevronsUpDown, GalleryVerticalEnd } from 'lucide-react';
import React from 'react';
import { toast } from 'react-toastify';

const OpunitSwitcher: React.FC = () => {
  const { activeOpunit, setActiveOpunit } = useAuth();
  const { data: opunits, isLoading: opunitsLoading } = useGetMyOpunits();

  const { mutateAsync: setActiveOpunitAsync } = useSetActiveOpunit();
  const handelOpunitChange = (opunitId: string) => {
    try {
      setActiveOpunitAsync(opunitId);
      setActiveOpunit(opunitId);
      toast.success('Opunit changed successfully');
    } catch (error) {
      console.log('Error changing opunit', error);
      toast.error('Error changing opunit');
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        {opunitsLoading ||
          (!opunits && (
            <div className="flex items-center justify-center py-4 h-10">
              <Spinner size={'sm'} color="blue-500" />
            </div>
          ))}
        {!opunitsLoading && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <GalleryVerticalEnd className="size-4" />
                </div>
                <div className="flex flex-col gap-0.5 leading-none">
                  <span className="font-semibold">Operational Unit</span>
                  <span className="">
                    {opunits?.data.find((opunit: any) => opunit.value === activeOpunit)?.label || 'Select'}
                  </span>
                </div>
                <ChevronsUpDown className="ml-auto" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width]" align="start">
              {opunits?.data.map((opunit: any) => (
                <DropdownMenuItem key={opunit.value} onSelect={() => handelOpunitChange(opunit.value)}>
                  {opunit.label} {opunit.value === activeOpunit && <Check className="ml-auto" />}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </SidebarMenuItem>
    </SidebarMenu>
  );
};

export default OpunitSwitcher;
