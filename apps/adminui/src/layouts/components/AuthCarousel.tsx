import React from 'react';
import Autoplay from 'embla-carousel-autoplay';
import { Carousel, CarouselContent, CarouselItem } from '@adminui/components/ui/carousel';
import { AuthSlideFour, AuthSlideOne, AuthSlideThree, AuthSlideTwo } from '@adminui/assets';

const AuthCarousel: React.FC = () => {
  return (
    <Carousel
      className="w-full mt-0 mb-0"
      plugins={[
        Autoplay({
          delay: 2000,
        }),
      ]}
    >
      <CarouselContent>
        {[AuthSlideOne, AuthSlideTwo, AuthSlideThree, AuthSlideFour].map((src, index) => (
          <CarouselItem key={index}>
            <div className="p-1">
              <div className="image-container">
                <img src={src} alt={`Carousel image ${index + 1}`} className="rounded-md object-cover" />
              </div>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};

export default AuthCarousel;
