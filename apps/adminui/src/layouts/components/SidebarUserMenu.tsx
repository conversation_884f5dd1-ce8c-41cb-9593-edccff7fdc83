import { AvatarPlaceHolder } from '@adminui/assets';
import { Avatar, AvatarFallback, AvatarImage } from '@adminui/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@adminui/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@adminui/components/ui/sidebar';
import { useLogout } from '@adminui/modules/auth/services/auth-service';
import useAuth from '@adminui/store/AuthService';
import { ChevronsUpDown, BadgeCheck, CreditCard, Bell, LogOut } from 'lucide-react';
import React from 'react';
import { toast } from 'react-toastify';

interface Props {
  menuPosition?: 'sidebar' | 'header';
}

const SidebarUserMenu: React.FC<Props> = ({ menuPosition = 'sidebar' }) => {
  const { isMobile } = useSidebar();
  const { userData } = useAuth();

  const { mutateAsync: handleLogout } = useLogout();

  const handelLogout = async () => {
    try {
      await handleLogout();
      toast.success('Logout successful! Redirecting...');
    } catch (error) {
      console.error('Logout failed:', error); // Log the error for debugging
      toast.error('Failed to log out. Please try again.'); // Show error notification
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={AvatarPlaceHolder} alt={'avatar'} />
                <AvatarFallback className="rounded-lg">DP</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{userData?.name}</span>
                <span className="truncate text-xs">{userData?.emailId}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile || menuPosition === 'header' ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={'/avatar.jpg'} alt={'avatar'} />
                  <AvatarFallback className="rounded-lg">RY</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{userData?.name}</span>
                  <span className="truncate text-xs">{userData?.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <BadgeCheck />
                Account
              </DropdownMenuItem>
              <DropdownMenuItem>
                <CreditCard />
                Billing
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bell />
                Notifications
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="cursor-pointer" onClick={handelLogout}>
              <LogOut />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
};

export default SidebarUserMenu;
