import { Separator } from '@adminui/components/ui/separator';
import { SidebarTrigger } from '@adminui/components/ui/sidebar';
import { BreadcrumbItem } from '@adminui/types/breadcrumb-type';
import React from 'react';
import Breadcrumbs from '../components/Breadcrumbs';
import SidebarUserMenu from '../components/SidebarUserMenu';

interface Props {
  breadcrumbItems: BreadcrumbItem[];
}

const AppHeader: React.FC<Props> = ({ breadcrumbItems }) => {
  return (
    <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear ">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumbs breadcrumbItems={breadcrumbItems} />
      </div>

      <div className="flex items-center gap-2 px-4">
        <SidebarUserMenu menuPosition="header" />
      </div>
    </header>
  );
};

export default AppHeader;
