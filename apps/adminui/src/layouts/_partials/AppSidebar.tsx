import * as React from 'react';
import { LayoutDashboardIcon, Store, UserCog, Users } from 'lucide-react';

import {
  Sidebar,
  <PERSON>barContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from '@adminui/components/ui/sidebar';
import { LogoIcon } from '@adminui/assets';
import { NavLink, useLocation } from 'react-router-dom';
import OpunitSwitcher from '../components/OpunitSwitcher';
import { useGetModulesAndGroupsPermission } from '@adminui/modules/auth/services/auth-service';
import Spinner from '@adminui/components/common/Spinner';

type Module = { SidebarNav?: React.FC<{ isActive: boolean }>; NAV_GROUP?: string; BASE_PATH?: string };
const modules: Record<string, Module> = import.meta.glob('../../modules/**/Route.tsx', { eager: true });

export const AppSidebar = ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
  const location = useLocation();
  const { pathname } = location;
  const { state } = useSidebar();

  const { data: response, isLoading: modulesAndGroupsLoading } = useGetModulesAndGroupsPermission();

  const hasModuleAccess = (modules: string[]) => {
    if (!response?.data?.modules) {
      return false;
    }
    const myModules = response?.data?.modules;
    if (myModules.includes('FULL_ACCESS')) {
      return true;
    }
    return modules.some((module) => myModules.includes(module));
  };

  const menuItems = Object.values(modules).reduce(
    (acc, mod) => {
      if (mod.SidebarNav && mod.NAV_GROUP && mod.BASE_PATH) {
        const Component = mod.SidebarNav;
        const group = mod.NAV_GROUP;
        if (!acc[group]) {
          acc[group] = [];
        }
        acc[group].push(<Component key={mod.BASE_PATH} isActive={pathname.includes(mod.BASE_PATH!)} />);
      }
      return acc;
    },
    {} as Record<string, Array<React.JSX.Element>>,
  );

  /* const hasGroupAccess = (groups: string[]) => {
    if (!response?.data?.groups) {
      return false;
    }
    const myGroups = response?.data?.groups;
    if (myGroups.includes('FULL_ACCESS')) {
      return true;
    }
    return groups.some((group) => myGroups.includes(group));
  }; */

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <div className="flex flex-row items-center py-2 text-sidebar-accent-foreground">
          <div className="flex aspect-square items-center justify-center rounded-lg mr-2">
            <img src={LogoIcon} alt="Company Logo" width={40} height={40} />
          </div>
          {state === 'expanded' && (
            <div className="text-center text-lg leading-tight text-red-700">Dey's Publishing</div>
          )}
        </div>
      </SidebarHeader>
      {(modulesAndGroupsLoading || !response?.data) && (
        <div className="flex items-center justify-center h-32">
          <Spinner size={'sm'} color="blue-500" />
        </div>
      )}
      {!modulesAndGroupsLoading && response?.data && (
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Overview</SidebarGroupLabel>
            <SidebarMenu>
              <SidebarMenuItem key="Dashboard">
                <SidebarMenuButton asChild tooltip="Dashboard" isActive={pathname.includes('dashboard')}>
                  <NavLink to="/dashboard">
                    <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                    <span>Dashboard</span>
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem key="category">
                <SidebarMenuButton asChild tooltip="Category" isActive={pathname.includes('category')}>
                  <NavLink to="/category">
                    <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                    <span>Category</span>
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem key="products">
                <SidebarMenuButton asChild tooltip="Category" isActive={pathname.includes('products')}>
                  <NavLink to="/products">
                    <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                    <span>Products</span>
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
          {Object.keys(menuItems).map((navGroup) => (
            <SidebarGroup>
              <SidebarGroupLabel>{navGroup}</SidebarGroupLabel>
              <SidebarMenu>{menuItems[navGroup]}</SidebarMenu>
            </SidebarGroup>
          ))}
          {hasModuleAccess(['usermgmt', 'rbac']) && (
            <SidebarGroup>
              <SidebarGroupLabel>ACL</SidebarGroupLabel>
              {hasModuleAccess(['usermgmt']) && (
                <SidebarMenu>
                  <SidebarMenuItem key="Users">
                    <SidebarMenuButton asChild tooltip="Users" isActive={pathname.includes('users')}>
                      <NavLink to="/users">
                        <Users className="mr-2 h-4 w-4" />
                        <span>Users</span>
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              )}
              {hasModuleAccess(['rbac']) && (
                <SidebarMenu>
                  <SidebarMenuItem key="Roles">
                    <SidebarMenuButton asChild tooltip="Roles" isActive={pathname.includes('roles')}>
                      <NavLink to="/roles">
                        <UserCog className="mr-2 h-4 w-4" />
                        <span>Roles</span>
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              )}
              {hasModuleAccess(['opunitmgmt']) && (
                <SidebarMenu>
                  <SidebarMenuItem key="Operational Units">
                    <SidebarMenuButton asChild tooltip="Operational Units" isActive={pathname.includes('opunits')}>
                      <NavLink to="/opunits">
                        <Store className="mr-2 h-4 w-4" />
                        <span>Operational Units</span>
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              )}
            </SidebarGroup>
          )}
        </SidebarContent>
      )}
      <SidebarFooter>
        <OpunitSwitcher />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};
