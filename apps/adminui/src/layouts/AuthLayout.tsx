import { Logo } from '@adminui/assets';
import TopProgressBar from '@adminui/components/common/TopProgressBar';
import React, { ReactNode } from 'react';
import { Slide, ToastContainer } from 'react-toastify';
import AuthCarousel from './components/AuthCarousel';

interface Props {
  children: ReactNode;
}

const AuthLayout: React.FC<Props> = ({ children }) => {
  return (
    <main className="min-h-screen">
      <TopProgressBar />
      <div className="min-h-screen grid lg:grid-cols-2">
        <div className="flex flex-col bg-slate-900 p-10 text-white dark:border-r">
          <div className="flex items-center mb-10">
            <img src={Logo} alt="logo" width={200} />
          </div>
          <div className="w-full">
            <AuthCarousel />
          </div>
          <div className="mt-auto">
            <blockquote className="space-y-2">
              <p className="text-lg">
                This platform has streamlined our entire book management process, making it easier to find and manage
                top-selling books.
              </p>
              <footer className="text-sm">Deys Publishing House</footer>
            </blockquote>
          </div>
        </div>
        <div className="flex flex-col justify-center items-center">{children}</div>
      </div>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        transition={Slide}
      />
    </main>
  );
};

export default AuthLayout;
