import TopProgressBar from '@adminui/components/common/TopProgressBar';
import { SidebarInset, SidebarProvider } from '@adminui/components/ui/sidebar';
import React, { ReactNode } from 'react';
import { AppSidebar } from './_partials/AppSidebar';
import { BreadcrumbItem } from '@adminui/types/breadcrumb-type';
import AppHeader from './_partials/AppHeader';
import { Slide, ToastContainer } from 'react-toastify';
import { Loader } from 'lucide-react';

interface Props {
  children: ReactNode;
  breadcrumbItems: BreadcrumbItem[];
  className?: string;
  isLoading?: boolean;
}
const MainLayout: React.FC<Props> = (props: Props) => {
  const { children, className, breadcrumbItems, isLoading } = props;
  return (
    <SidebarProvider>
      <TopProgressBar />
      <AppSidebar />
      <SidebarInset>
        <AppHeader breadcrumbItems={breadcrumbItems} />
        <main className={className}>{children}</main>
        {isLoading ? (
          <div className="absolute inset-0 border bg-gray-700/40 z-10 flex items-center justify-center">
            <Loader size={50} className="animate-spin  duration-700" />
          </div>
        ) : null}
      </SidebarInset>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        transition={Slide}
      />
    </SidebarProvider>
  );
};

export default MainLayout;
