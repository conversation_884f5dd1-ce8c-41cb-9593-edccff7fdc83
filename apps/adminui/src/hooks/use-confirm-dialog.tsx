import ConfirmDialog from '@adminui/components/common/ConfirmDialog';
import { useState } from 'react';

type ConfirmDialogOptions = {
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
};

export const useConfirmDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [dialogOptions, setDialogOptions] = useState<ConfirmDialogOptions | null>(null);

  const showConfirmDialog = (options: ConfirmDialogOptions) => {
    setDialogOptions(options);
    setIsOpen(true);
  };

  const closeConfirmDialog = () => {
    setIsOpen(false);
    setDialogOptions(null);
  };

  const ConfirmDialogComponent = dialogOptions ? (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={closeConfirmDialog}
      onConfirm={() => {
        dialogOptions.onConfirm();
        closeConfirmDialog();
      }}
      title={dialogOptions.title}
      description={dialogOptions.description}
      confirmText={dialogOptions.confirmText}
      cancelText={dialogOptions.cancelText}
    />
  ) : null;

  return { showConfirmDialog, ConfirmDialogComponent };
};
