import { useGetUserPermissionsByModule } from '@adminui/modules/auth/services/auth-service';
import { useMemo } from 'react';

interface CanAccessProps {
  module: string;
  permissions: string[];
}

const useCanAccess = ({ module, permissions }: CanAccessProps): boolean => {
  const { data: response, isLoading } = useGetUserPermissionsByModule(module);

  const hasAccess = useMemo(() => {
    const myPermissions = response?.data || [];
    return myPermissions.includes('FULL_ACCESS') || permissions.some((p) => myPermissions.includes(p));
  }, [response, permissions]);
  return !isLoading && hasAccess;
};

export default useCanAccess;
