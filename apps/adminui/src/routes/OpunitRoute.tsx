import PageAccess from '@adminui/middlewares/PageAccess';
import OpunitCreatePage from '@adminui/modules/opunit/OpunitCreatePage';
import OpunitListPage from '@adminui/modules/opunit/OpunitListPage';
import { Outlet } from 'react-router-dom';

const OpunitRoute = [
  {
    path: '',
    element: <Outlet />,
    children: [
      {
        path: '',
        element: (
          <PageAccess module="opunitmgmt" permissions={['VIEW_OPUNIT', 'MANAGE_OPUNIT']}>
            <OpunitListPage />
          </PageAccess>
        ),
      },
      {
        path: 'create',
        element: (
          <PageAccess module="opunitmgmt" permissions={['MANAGE_OPUNIT']}>
            <OpunitCreatePage />
          </PageAccess>
        ),
      },
    ],
  },
];

export default OpunitRoute;
