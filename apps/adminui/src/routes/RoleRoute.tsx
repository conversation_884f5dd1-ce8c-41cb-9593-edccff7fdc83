import { Outlet } from 'react-router-dom';
import RoleCreatePage from '@adminui/modules/role/RoleCreatePage';
import RoleListPage from '@adminui/modules/role/RoleListPage';
import RoleEditPage from '@adminui/modules/role/RoleEditPage';
import PageAccess from '@adminui/middlewares/PageAccess';

const RoleRoute = [
  {
    path: '',
    element: <Outlet />,
    children: [
      {
        path: '',
        element: (
          <PageAccess module="rbac" permissions={['VIEW_ROLES', 'MANAGE_ROLE']}>
            <RoleListPage />
          </PageAccess>
        ),
      },
      {
        path: 'create',
        element: (
          <PageAccess module="rbac" permissions={['MANAGE_ROLE']}>
            <RoleCreatePage />
          </PageAccess>
        ),
      },
      {
        path: 'edit/:id',
        element: (
          <PageAccess module="rbac" permissions={['MANAGE_ROLE']}>
            <RoleEditPage />
          </PageAccess>
        ),
      },
    ],
  },
];

export default RoleRoute;
