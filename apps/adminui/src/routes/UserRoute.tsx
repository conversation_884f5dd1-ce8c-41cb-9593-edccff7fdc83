import PageAccess from '@adminui/middlewares/PageAccess';
import UserCreatePage from '@adminui/modules/user/UserCreatePage';
import UserListPage from '@adminui/modules/user/UserListPage';
import { Outlet } from 'react-router-dom';

const UserRoute = [
  {
    path: '',
    element: <Outlet />,
    children: [
      {
        path: '',
        element: (
          <PageAccess module="usermgmt" permissions={['VIEW_USERS', 'MANAGE_USER']}>
            <UserListPage />
          </PageAccess>
        ),
      },
      {
        path: 'create',
        element: (
          <PageAccess module="usermgmt" permissions={['MANAGE_USER']}>
            <UserCreatePage />
          </PageAccess>
        ),
      },
    ],
  },
];

export default UserRoute;
