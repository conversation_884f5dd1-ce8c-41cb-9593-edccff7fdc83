import { RouteObject, Outlet, useRoutes } from 'react-router-dom';
import AuthMiddleware from './middlewares/AuthMiddleware';
import RedirectIfAuthenticated from './middlewares/RedirectIfAuthenticated';
import LoginPage from './modules/auth/LoginPage';
import Dashboard from './modules/dashboard/Dashboard';
import NoAccessPage from './modules/errors/NoAccessPage';
import PageNotFound from './modules/errors/PageNotFound';
import UserRoute from './routes/UserRoute';
import OpunitRoute from './routes/OpunitRoute';
import RoleRoute from './routes/RoleRoute';
import ModuleAccess from './middlewares/ModuleAccess';
import Category from './modules/catalog/category';
import ProductRoute from './modules/productmgmt/ProductRoute';

const modules: Record<string, { BASE_PATH?: string; default: RouteObject[] }> = import.meta.glob(
  './modules/**/Route.tsx',
  { eager: true },
);

const App: React.FC = () => {
  const moduleRoutes = Object.values(modules).map((mod) => ({
    path: mod.BASE_PATH,
    element: <Outlet />,
    children: mod.default,
  }));

  const routes: RouteObject[] = [
    {
      path: '/',
      element: (
        <RedirectIfAuthenticated>
          <LoginPage />
        </RedirectIfAuthenticated>
      ),
    },
    {
      path: '/login',
      element: (
        <RedirectIfAuthenticated>
          <LoginPage />
        </RedirectIfAuthenticated>
      ),
    },
    {
      element: (
        <AuthMiddleware>
          <Outlet />
        </AuthMiddleware>
      ),
      children: [
        {
          path: 'dashboard',
          element: <Dashboard />,
        },
        {
          path: 'category',
          element: <Category />,
        },
        {
          path: 'products',
          element: <Outlet />,
          children: ProductRoute,
        },
        ...moduleRoutes,
        {
          path: 'users',
          element: (
            <ModuleAccess modules={['usermgmt']}>
              <Outlet />
            </ModuleAccess>
          ),
          children: UserRoute,
        },
        {
          path: 'roles',
          element: (
            <ModuleAccess modules={['rbac']}>
              <Outlet />
            </ModuleAccess>
          ),
          children: RoleRoute,
        },
        {
          path: 'opunits',
          element: (
            <ModuleAccess modules={['opunitmgmt']}>
              <Outlet />
            </ModuleAccess>
          ),
          children: OpunitRoute,
        },
      ],
    },
    {
      path: '/no-access',
      element: <NoAccessPage />,
    },
    {
      path: '*',
      element: <PageNotFound />,
    },
  ];
  return useRoutes(routes);
};
export default App;
