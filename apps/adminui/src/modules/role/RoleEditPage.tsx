import useDocumentTitle from '@adminui/hooks/useDocumentTitle';
import MainLayout from '@adminui/layouts/MainLayout';
import React, { useEffect, useMemo } from 'react';
import { useGetAllPermissions, useGetRoleById, useUpdateRole } from './services/role-service';
import { useNavigate, useParams } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import GoBackButton from '@adminui/components/buttons/GoBackButton';
import { Card, CardContent, CardFooter } from '@adminui/components/ui/card';
import { Form } from '@adminui/components/ui/form';
import SubmitButton from '@adminui/components/buttons/SubmitButton';
import TextInput from '@adminui/components/form-inputs/TextInput';
import YesNoInput from '@adminui/components/form-inputs/YesNoInput';
import TextAreaInput from '@adminui/components/form-inputs/TextAreaInput';
import PermissionsInput from './components/PermissionInput';
import { parseErrorResponse } from '@adminui/lib/api-error-parser';
import { toast } from 'react-toastify';
import Spinner from '@adminui/components/common/Spinner';

const breadcrumbItems = [
  { label: 'Dashboard', href: '/' },
  { label: 'Roles', href: '/roles' },
  { label: 'Create', isPage: true },
];

const validationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  is_read_only: z.boolean(),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
});

type formValues = z.infer<typeof validationSchema>;

const RoleEditPage: React.FC = () => {
  useDocumentTitle('Create Role');
  const navigate = useNavigate();
  const { id: roleId } = useParams<{ id: string }>();
  const { data: permissionList, isLoading: isPermissionsLoading } = useGetAllPermissions();
  const { data: roleData, isLoading: isRoleLoading, refetch } = useGetRoleById(roleId ?? '');

  const defaultValues = useMemo(
    () => ({
      name: '',
      description: '',
      is_read_only: false,
      permissions: [],
    }),
    [],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError, setValue, watch, formState } = form;
  const selectedPermissions = watch('permissions');

  useEffect(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    if (!isRoleLoading && roleData?.data) {
      setValue('name', roleData.data.name);
      setValue('description', roleData.data.description);
      setValue('is_read_only', roleData.data.isReadOnly);
      setValue(
        'permissions',
        roleData.data.permissions.map((p: any) => p.id.toString()),
      );
    }
  }, [roleData, setValue, isRoleLoading]);

  const handlePermissionChange = (permissionId: string, isChecked: boolean) => {
    const updatedPermissions = isChecked
      ? [...selectedPermissions, permissionId]
      : selectedPermissions.filter((id) => id !== permissionId);

    setValue('permissions', updatedPermissions, { shouldValidate: true });
  };

  const { mutateAsync: updateRole, isPending: loading } = useUpdateRole();
  const onSubmit = (data: formValues) => {
    const transformedData = {
      ...data,
      permissions: data.permissions.map((permission) => parseInt(permission, 10)),
    };
    console.log(transformedData);
    updateRole(
      { data: transformedData, id: String(roleId) },
      {
        onSuccess: () => {
          toast.success('Role updated successfully');
          navigate('/roles');
        },
        onError: (error: any) => {
          const { message, responseBody } = parseErrorResponse(error);
          toast.error(message);
          console.log(responseBody);
          if (responseBody?.message && Array.isArray(responseBody.message)) {
            responseBody.message.forEach((fieldError: any) => {
              const { property, errors } = fieldError;
              if (property && errors && Array.isArray(errors)) {
                setError(property, { message: errors.join(', ') });
              }
            });
          }
        },
      },
    );
  };

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page px-2 py-2">
        <div className="top-bar flex justify-between items-center px-4 py-2 bg-white rounded-md shadow-md">
          <div className="left-side text-xl font-semibold text-gray-700">Update Role</div>
          <div className="right-side">
            <div className="flex items-center gap-2">
              <GoBackButton href="/roles" label="Go Back" />
            </div>
          </div>
        </div>
        <div className="content mt-2">
          {!isRoleLoading && roleData?.data && (
            <Card className="rounded-md shadow-md">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <TextInput
                          control={form.control}
                          name="name"
                          label="Name"
                          placeholder="Enter Role name"
                          isRequired
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <YesNoInput
                          control={form.control}
                          name="is_read_only"
                          label="Is Read Only"
                          placeholder="Read Only"
                        />
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextAreaInput
                        control={form.control}
                        name="description"
                        label="Role Description"
                        placeholder="Description"
                      />
                    </div>
                    <div className="flex flex-col space-y-1 mt-2">
                      <h2 className="text-xl font-semibold mb-2">Permissions</h2>
                      {isPermissionsLoading ? (
                        <p>Loading permissions...</p>
                      ) : (
                        <div>
                          <PermissionsInput
                            permissions={permissionList?.data}
                            selectedPermissions={selectedPermissions}
                            onPermissionChange={handlePermissionChange}
                          />
                          {formState.errors.permissions && (
                            <p className="text-red-500 text-sm mt-2">{formState.errors.permissions.message}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end p-4">
                    <SubmitButton label="Update Role" isLoading={loading} />
                  </CardFooter>
                </form>
              </Form>
            </Card>
          )}
          {isRoleLoading && (
            <div className="flex items-center justify-center h-32">
              <Spinner size={'sm'} color="blue-500" />
            </div>
          )}
          {!isRoleLoading && !roleData?.data && (
            <div className="flex items-center justify-center h-32">
              <p>Role not found</p>
            </div>
          )}
        </div>
      </section>
    </MainLayout>
  );
};

export default RoleEditPage;
