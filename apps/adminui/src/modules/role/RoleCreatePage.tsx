import useDocumentTitle from '@/hooks/useDocumentTitle';
import MainLayout from '@/layouts/MainLayout';
import React, { useMemo } from 'react';
import { useCreateRole, useGetAllPermissions } from './services/role-service';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import GoBackButton from '@/components/buttons/GoBackButton';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import SubmitButton from '@/components/buttons/SubmitButton';
import TextInput from '@/components/form-inputs/TextInput';
import YesNoInput from '@/components/form-inputs/YesNoInput';
import TextAreaInput from '@/components/form-inputs/TextAreaInput';
import PermissionsInput from './components/PermissionInput';
import { parseErrorResponse } from '@/lib/api-error-parser';
import { toast } from 'react-toastify';

const breadcrumbItems = [
  { label: 'Dashboard', href: '/' },
  { label: 'Roles', href: '/roles' },
  { label: 'Create', isPage: true },
];

const validationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  is_read_only: z.boolean(),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
});

type formValues = z.infer<typeof validationSchema>;

const RoleCreatePage: React.FC = () => {
  useDocumentTitle('Create Role');
  const navigate = useNavigate();
  const { data: permissionList, isLoading: isPermissionsLoading } = useGetAllPermissions();

  const defaultValues = useMemo(
    () => ({
      name: '',
      description: '',
      is_read_only: false,
      permissions: [],
    }),
    [],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError, setValue, watch, formState } = form;
  const selectedPermissions = watch('permissions');

  const handlePermissionChange = (permissionId: string, isChecked: boolean) => {
    const updatedPermissions = isChecked
      ? [...selectedPermissions, permissionId]
      : selectedPermissions.filter((id) => id !== permissionId);

    setValue('permissions', updatedPermissions, { shouldValidate: true });
  };
  const { mutateAsync: createRole, isPending: loading } = useCreateRole();

  const onSubmit = (data: formValues) => {
    const transformedData = {
      ...data,
      permissions: data.permissions.map((permission) => parseInt(permission, 10)),
    };
    createRole(transformedData, {
      onSuccess: (data) => {
        console.log(data);
        toast.success('Role created successfully');
        navigate('/roles');
      },
      onError: (error) => {
        const { message, responseBody } = parseErrorResponse(error);
        toast.error(message);
        console.log(responseBody);
        if (responseBody?.message && Array.isArray(responseBody.message)) {
          responseBody.message.forEach((fieldError: any) => {
            const { property, errors } = fieldError;
            if (property && errors && Array.isArray(errors)) {
              setError(property, { message: errors.join(', ') });
            }
          });
        }
      },
    });
  };

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page px-2 py-2">
        <div className="top-bar flex justify-between items-center px-4 py-2 bg-white rounded-md shadow-md">
          <div className="left-side text-xl font-semibold text-gray-700">Create Role</div>
          <div className="right-side">
            <div className="flex items-center gap-2">
              <GoBackButton href="/roles" label="Go Back" />
            </div>
          </div>
        </div>
        <div className="content mt-2">
          <Card className="rounded-md shadow-md">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextInput
                        control={form.control}
                        name="name"
                        label="Name"
                        placeholder="Enter Role name"
                        isRequired
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <YesNoInput
                        control={form.control}
                        name="is_read_only"
                        label="Is Read Only"
                        placeholder="Read Only"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col space-y-1 min-h-[80px]">
                    <TextAreaInput
                      control={form.control}
                      name="description"
                      label="Role Description"
                      placeholder="Description"
                    />
                  </div>
                  <div className="flex flex-col space-y-1 mt-2">
                    <h2 className="text-xl font-semibold mb-2">Permissions</h2>
                    {isPermissionsLoading ? (
                      <p>Loading permissions...</p>
                    ) : (
                      <div>
                        <PermissionsInput
                          permissions={permissionList?.data}
                          selectedPermissions={selectedPermissions}
                          onPermissionChange={handlePermissionChange}
                        />
                        {formState.errors.permissions && (
                          <p className="text-red-500 text-sm mt-2">{formState.errors.permissions.message}</p>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end p-4">
                  <SubmitButton label="Create Role" isLoading={loading} />
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </section>
    </MainLayout>
  );
};

export default RoleCreatePage;
