import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from '@adminui/components/ui/drawer';
import React from 'react';
import { useGetRoleById } from '../services/role-service';
import { Button } from '@adminui/components/ui/button';
import Spinner from '@adminui/components/common/Spinner';
import { groupConfig, moduleConfig } from '../services/permission-mapping';

interface Props {
  roleId: number | null;
  onClose: () => void;
}

const ViewPermission: React.FC<Props> = ({ roleId, onClose }) => {
  const { data: roleData, isLoading: isRoleLoading } = useGetRoleById(String(roleId));
  const permissions = roleData?.data?.permissions || [];
  return (
    <React.Fragment>
      <Drawer open={!!roleId} onOpenChange={(isOpen) => !isOpen && onClose()} direction="right" autoFocus={!!roleId}>
        <DrawerContent>
          <DrawerHeader className="border-b shadow-md">
            <DrawerTitle>Permissions</DrawerTitle>
            <DrawerDescription>All permissions assigned to this role</DrawerDescription>
          </DrawerHeader>
          <div className="content mt-2 px-2 max-h-screen overflow-y-auto">
            {isRoleLoading ? (
              <div className="flex items-center justify-center h-32">
                <Spinner size={'sm'} color="blue-500" />
              </div>
            ) : (
              <div className="p-1.5 permissions-lis">
                {permissions.map((permission: any) => (
                  <div key={permission.id} className="border rounded-lg p-4 shadow-sm bg-white mb-2">
                    <h3 className="text-md font-semibold text-gray-800">{permission.name}</h3>
                    <p className="text-sm text-gray-600">{permission.description}</p>
                    <div className="mt-2 text-sm text-gray-500">
                      <span className="font-medium">Module:</span>{' '}
                      {moduleConfig[permission.module] || permission.module}
                    </div>
                    <div className="text-sm text-gray-500">
                      <span className="font-medium">Group:</span> {groupConfig[permission.groups] || permission.groups}
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div className="flex justify-end p-1.5">
              <Button type="button" variant="outline" onClick={onClose} className="px-6">
                Cancel
              </Button>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </React.Fragment>
  );
};

export default ViewPermission;
