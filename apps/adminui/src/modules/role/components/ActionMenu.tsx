import { Button } from '@adminui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@adminui/components/ui/dropdown-menu';
import { EditIcon, Trash2, UserLock } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';
import { useDeleteRole } from '../services/role-service';
import { toast } from 'react-toastify';
import { useConfirmDialog } from '@adminui/hooks/use-confirm-dialog';

type Props = {
  dataId: number;
  isReadOnly?: boolean;
  onViewClick: (dataId: number) => void;
};

const ActionMenu: React.FC<Props> = ({ dataId, isReadOnly, onViewClick }) => {
  const { showConfirmDialog, ConfirmDialogComponent } = useConfirmDialog();
  const handleViewClick = () => {
    onViewClick(dataId);
  };

  const { mutateAsync: handleDelete } = useDeleteRole();

  const handleDeleteClick = async () => {
    showConfirmDialog({
      title: 'Are you absolutely sure?',
      description: 'This action cannot be undone. This will permanently delete the role.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          await handleDelete(String(dataId));
          toast.success('Role deleted successfully');
        } catch (error) {
          toast.error('Failed to delete role');
          console.log(error);
        }
      },
    });
  };

  return (
    <div className="flex items-center justify-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size={'sm'}>Action</Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {!isReadOnly && (
            <DropdownMenuItem asChild>
              <Link to={`/roles/edit/${dataId}`}>
                <EditIcon className="mr-2 h-4 w-4" />
                <span>Edit</span>
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem onClick={handleViewClick}>
            <UserLock className="mr-2 h-4 w-4" />
            <span>View Permissions</span>
          </DropdownMenuItem>
          {!isReadOnly && (
            <DropdownMenuItem onClick={handleDeleteClick}>
              <Trash2 className="mr-2 h-4 w-4" />
              <span>Remove</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      {ConfirmDialogComponent}
    </div>
  );
};

export default ActionMenu;
