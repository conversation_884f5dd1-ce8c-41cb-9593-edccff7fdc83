import { FormControl, FormDescription, FormItem, FormLabel } from '@adminui/components/ui/form';
import { Switch } from '@adminui/components/ui/switch';
import React from 'react';
import { groupConfig, moduleConfig } from '../services/permission-mapping';

interface Permission {
  id: number;
  name: string;
  description: string;
  module: string;
  groups: string | null;
}

interface Props {
  permissions: Record<string, Record<string, Permission[]>>;
  selectedPermissions: string[];
  onPermissionChange: (permissionId: string, isChecked: boolean) => void;
}

const PermissionsInput: React.FC<Props> = ({ permissions, selectedPermissions, onPermissionChange }) => {
  return (
    <div>
      {Object.entries(permissions).map(([module, groups]) => (
        <div key={module} className="mb-6">
          <h3 className="text-lg font-semibold mb-4 capitalize">{moduleConfig[module] || module}</h3>
          {Object.entries(groups).map(([group, groupPermissions]) => (
            <div key={group} className="mb-4">
              <h4 className="text-md font-medium mb-2">{groupConfig[group] || group}</h4>
              <div className="grid grid-cols-2 gap-4">
                {groupPermissions.map(({ id, name, description }) => (
                  <FormItem key={id} className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div>
                      <FormLabel>{name}</FormLabel>
                      <FormDescription>{description}</FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={selectedPermissions.includes(id.toString())}
                        onCheckedChange={(checked) => onPermissionChange(id.toString(), checked)}
                      />
                    </FormControl>
                  </FormItem>
                ))}
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default PermissionsInput;
