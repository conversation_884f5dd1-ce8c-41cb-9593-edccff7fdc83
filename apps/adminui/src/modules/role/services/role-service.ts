import { apiClient } from '@adminui/lib/api-client';
import { ApiResponseType } from '@common-utils/api-types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

type RoleParams = {
  keyword?: string;
  page?: number;
  limit?: number; // Changed to number for consistency
};

type RoleListResponse = {
  items: Role[];
  total: number;
  page: number;
  limit: number;
};

export type Role = {
  id: number;
  name: string;
  description: string;
  isReadOnly: string;
  rolePosition: number;
};

// Get Role List with Pagination
export const useGetRoleList = (reqParams: RoleParams) => {
  const { keyword, page, limit } = reqParams;
  const params = { page, keyword, limit };

  return useQuery({
    queryKey: ['role-list', params],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<RoleListResponse>>('/roles', {
        params,
      });
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

//Get all Permissions List
export const useGetAllPermissions = () => {
  return useQuery({
    queryKey: ['permissions'],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>('/permissions');
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

// Get Role By Id
export const useGetRoleById = (id: string) => {
  return useQuery({
    queryKey: ['role-by-id', id],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>(`/roles/${id}`);
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

//Create Role
export const useCreateRole = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const { data: response } = await apiClient.post<ApiResponseType<any>>('/roles', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['role-list'] });
    },
  });
};

//Update Role
export const useUpdateRole = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ data, id }: { data: any; id: string }) => {
      const { data: response } = await apiClient.put<ApiResponseType<any>>(`/roles/${id}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['role-list'] });
    },
  });
};

//Delete Role
export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete<ApiResponseType<any>>(`/roles/${id}`);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['role-list'] });
    },
  });
};
