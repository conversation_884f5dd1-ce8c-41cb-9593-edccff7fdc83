import { apiClient } from '@adminui/lib/api-client';
import { ApiResponseType } from '@common-utils/api-types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const { data: response } = await apiClient.post<ApiResponseType<any>>('/category', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['category-list'] });
    },
  });
};
