import SingleImageFormField, { SingleImageFormFieldRef } from '@/modules/media_asset/single-image-form-field';
import useDocumentTitle from '@/hooks/useDocumentTitle';
import MainLayout from '@/layouts/MainLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useRef } from 'react';
import { useCreateCategory } from './category-service';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Category', isPage: true },
];

const catgeoryFormValidationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  categoryImageId: z.number().optional(),
  parentCategoryId: z.string().optional(),
});
type CategoryFormModel = z.infer<typeof catgeoryFormValidationSchema>;

const Category = () => {
  useDocumentTitle('Dashboard');
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CategoryFormModel>({
    resolver: zodResolver(catgeoryFormValidationSchema),
    defaultValues: {
      name: '',
      description: '',
      categoryImageId: undefined,
      parentCategoryId: '',
    },
  });
  const imageRef = useRef<SingleImageFormFieldRef>(null);
  const { mutateAsync: createCategory, isPending: loading } = useCreateCategory();

  const onSubmit = async (data: CategoryFormModel) => {
    try {
      const mediaAsset = await imageRef.current?.uploadImage();
      if (mediaAsset) {
        data.categoryImageId = mediaAsset.id;
      }
      const response = await createCategory(data);
      if (response.status === 'success') {
        reset();
        imageRef.current?.reset();
        console.log('Category created successfully');
      }
    } catch (error) {
      console.error('Error creating category:', error);
    }
  };

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page p-6">
        <div className="top-bar flex justify-between items-center px-4 py-2 bg-white rounded-md shadow-md mb-4">
          <div className="left-side text-xl font-semibold text-gray-700">Create Category</div>
          <div className="right-side">
            <div className="flex items-center gap-2">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                onClick={() => window.history.back()}
              >
                Go Back
              </button>
            </div>
          </div>
        </div>

        <div className="content">
          <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-6 rounded-md shadow-md">
            <div className="grid grid-cols-1 gap-4">
              {/* Category Image at the top */}
              <div className="flex flex-col space-y-1 min-h-[80px]">
                <label className="text-sm font-medium text-gray-700">Category Image</label>
                <SingleImageFormField folderName={'category_image'} ref={imageRef} mediaAssetId={6} />
                {errors.categoryImageId && (
                  <p className="text-red-500 text-xs mt-1">{errors.categoryImageId.message}</p>
                )}
              </div>

              {/* Name field */}
              <div className="flex flex-col space-y-1 min-h-[80px]">
                <label className="text-sm font-medium text-gray-700">Name</label>
                <input
                  {...control.register('name')}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter category name"
                />
                {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>}
              </div>

              {/* Description field */}
              <div className="flex flex-col space-y-1 min-h-[80px]">
                <label className="text-sm font-medium text-gray-700">Description</label>
                <textarea
                  {...control.register('description')}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter category description"
                  rows={3}
                />
                {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description.message}</p>}
              </div>

              {/* Parent Category field */}
              <div className="flex flex-col space-y-1 min-h-[80px]">
                <label className="text-sm font-medium text-gray-700">Parent Category</label>
                <select
                  {...control.register('parentCategoryId')}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select parent category (optional)</option>
                  {/* TODO: Add parent category options from API */}
                </select>
                {errors.parentCategoryId && (
                  <p className="text-red-500 text-xs mt-1">{errors.parentCategoryId.message}</p>
                )}
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              >
                {isSubmitting ? 'Creating...' : 'Create Category'}
              </button>
            </div>
          </form>
        </div>
      </section>
    </MainLayout>
  );
};

export default Category;
