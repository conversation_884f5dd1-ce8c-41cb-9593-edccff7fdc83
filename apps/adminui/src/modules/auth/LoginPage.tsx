import SubmitButton from '@adminui/components/buttons/SubmitButton';
import EmailInput from '@adminui/components/form-inputs/EmailInput';
import PasswordInput from '@adminui/components/form-inputs/PasswordInput';
import { Card, CardContent, CardFooter, CardHeader } from '@adminui/components/ui/card';
import { Form } from '@adminui/components/ui/form';
import AuthLayout from '@adminui/layouts/AuthLayout';
import { useLogin } from '@adminui/modules/auth/services/auth-service';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string(),
  password: z.string(),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const LoginPage: React.FC = () => {
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: 'password',
    },
  });

  const {
    setError,
    formState: { errors },
  } = form;

  const { mutateAsync: handleLogin, isPending: isLoading, isError } = useLogin();

  async function onSubmit(data: LoginFormValues) {
    try {
      await handleLogin(data);
      toast.success('Login successful! Redirecting...');
      // Add any redirection logic here if needed
    } catch (error: any) {
      if (error?.response?.data) {
        // Handle validation errors (422)
        if (error.response.status === 422) {
          const validationErrors = error.response.data;
          Object.keys(validationErrors).forEach((field) => {
            setError(field as keyof LoginFormValues, {
              type: 'server',
              message: (validationErrors[field] as string[]).join(', '),
            });
          });
        } else {
          // Handle other server errors
          const errorMessage = error.response.data.message || 'An unexpected error occurred.';
          setError('root', {
            type: 'server',
            message: errorMessage,
          });
          toast.error(errorMessage);
        }
      } else {
        // Handle network or unknown errors
        setError('root', {
          type: 'server',
          message: 'Network error. Please try again later.',
        });
        toast.error('Network error. Please try again later.');
      }
    }
  }

  return (
    <AuthLayout>
      <div className="mx-auto flex flex-col justify-center space-y-6 w-[80%] h-[60%]">
        <Card>
          <CardHeader className="space-y-1">
            <h2 className="text-2xl font-semibold tracking-tight">Welcome back</h2>
            <p className="text-sm text-muted-foreground">Enter your credentials to access your dashboard</p>
          </CardHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CardContent className="grid gap-4">
                <EmailInput
                  control={form.control}
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                  isRequired
                />
                <PasswordInput
                  control={form.control}
                  name="password"
                  label="Password"
                  placeholder="Enter your password"
                  isRequired
                />
                {isError && <p className="text-red-500 text-sm">{errors.root?.message}</p>}
              </CardContent>
              <CardFooter className="flex justify-end mt-3">
                <SubmitButton label="Login" isLoading={isLoading} />
              </CardFooter>
            </form>
          </Form>
        </Card>
      </div>
    </AuthLayout>
  );
};

export default LoginPage;
