import { apiClient } from '@adminui/lib/api-client';
import useAuth from '@adminui/store/AuthService';
import { ApiResponseType } from '@common-utils/api-types';
import { LoginData } from '@adminui/types/login-data';
import { useMutation, useQuery } from '@tanstack/react-query';

export const useLogin = () => {
  const { login, setToken, updateUser, setActiveOpunit } = useAuth();
  return useMutation({
    mutationFn: async (payload: LoginData) => {
      const LoginPayload = {
        username: payload.email,
        password: payload.password,
      };
      const {
        data: { data: response },
      } = await apiClient.post('/auth/login', LoginPayload);
      login();
      setToken(response.token);
      updateUser(response.user);
      setActiveOpunit(response.activeOpUnite);
      return response;
    },
  });
};

export const useLogout = () => {
  const { logOut } = useAuth();
  return useMutation({
    mutationFn: async () => {
      await apiClient.post('/auth/logout');
      logOut();
    },
  });
};

//Get all operational units
export const useGetMyOpunits = () => {
  return useQuery({
    queryKey: ['get-my-opunits'],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>('/auth/get-my-opunits');
      return data;
    },
    staleTime: 5000,
    retry: false,
  });
};

// Set active opunit
export const useSetActiveOpunit = () => {
  return useMutation({
    mutationFn: async (opunitId: string) => {
      const { data } = await apiClient.put('/auth/change-active-opunit', {
        opunitId: opunitId,
      });
      return data;
    },
  });
};

// Get all Modules and Groups
export const useGetModulesAndGroupsPermission = () => {
  return useQuery({
    queryKey: ['get-modules-and-groups-permission'],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>('/auth/get-my-module-access');
      return data;
    },
    staleTime: 5000,
    retry: false,
  });
};

// Get All Permissions By Module To a User
export const useGetUserPermissionsByModule = (module: string) => {
  return useQuery({
    queryKey: ['get-all-permissions-by-module', module],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>(`/auth/get-my-permissions-by-module/${module}`);
      return data;
    },
    staleTime: 5000,
    retry: false,
  });
};
