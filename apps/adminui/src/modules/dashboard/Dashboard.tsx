import useDocumentTitle from '@adminui/hooks/useDocumentTitle';
import MainLayout from '@adminui/layouts/MainLayout';
import React from 'react';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Dashboard', isPage: true },
];

const Dashboard: React.FC = () => {
  useDocumentTitle('Dashboard');

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page px-2 py-2">
        <div className="top-counter-section">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2"></div>
        </div>
        <div className="bottom-counter-section"></div>
      </section>
    </MainLayout>
  );
};

export default Dashboard;
