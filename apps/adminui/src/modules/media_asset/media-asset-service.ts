import { apiClient } from '@adminui/lib/api-client';
import { ApiResponseType } from '@common-utils/api-types';
import { type QueryClient, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export type MediaAssetResponse = {
  id: number;
  fileName: string;
  fileURL: string;
  thumbnailURL: string;
  fileType: 'image' | 'video';
  folderName: string;
  metadata: Record<string, any>;
};

export const useUpsertMediaAsset = () => {
  return useMutation<
    ApiResponseType<MediaAssetResponse>,
    Error,
    { mediaAssetId?: number; data: FormData },
    { queryClient: QueryClient }
  >({
    mutationFn: async (params) => {
      const { mediaAssetId, data } = params;
      if (mediaAssetId) {
        const { data: response } = await apiClient.put(`/media-asset/${mediaAssetId}`, data);
        return response;
      }
      const { data: response } = await apiClient.post('/media-asset', data);
      return response;
    },
    onSuccess: (_, variables, context) => {
      context?.queryClient?.invalidateQueries({ queryKey: ['media-asset', variables.mediaAssetId] });
    },
  });
};

export const fetchMediaAsset = async (mediaAssetId: number) => {
  const { data } = await apiClient.get<ApiResponseType<MediaAssetResponse>>(`/media-asset/${mediaAssetId}`);
  return data;
};

export const useFetchMediaAsset = (mediaAssetId?: number) => {
  return useQuery({
    queryKey: ['media-asset', mediaAssetId],
    queryFn: async () => {
      const { data } = await fetchMediaAsset(mediaAssetId!);
      return data;
    },
    staleTime: 5000,
    retry: false,
    enabled: Boolean(mediaAssetId),
  });
};
