import { <PERSON>, CardContent, CardFooter } from '@adminui/components/ui/card';
import { cn } from '@adminui/lib/utils';
import { Image as ImageIcon } from 'lucide-react';
import { ChangeEventHandler, forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { MediaAssetResponse, useFetchMediaAsset, useUpsertMediaAsset } from './media-asset-service';

export type SingleImageFormFieldProps = {
  mediaAssetId?: number;
  folderName: string;
  className?: string;
  width?: string;
  height?: string;
};

export type SingleImageFormFieldRef = {
  uploadImage: () => Promise<MediaAssetResponse | undefined>;
  reset: () => void;
};

/**
 * This component is designed to be used with react-hook-form
 */
const SingleImageFormField = forwardRef<SingleImageFormFieldRef, SingleImageFormFieldProps>((props, ref) => {
  const { mediaAssetId, className, folderName, width = 'w-60', height = 'h-60' } = props;
  const [imageSrc, setImageSrc] = useState<string>();
  const [selectedFile, setSelectedFile] = useState<File>();
  const { data: imageAsset } = useFetchMediaAsset(mediaAssetId);
  const { mutateAsync: upsertMediaAsset } = useUpsertMediaAsset();

  useEffect(() => {
    if (mediaAssetId && imageAsset) {
      setImageSrc(imageAsset.fileURL);
      setSelectedFile(undefined);
    }
  }, [mediaAssetId, imageAsset]);

  useImperativeHandle(ref, () => ({
    uploadImage,
    reset: resetField,
  }));

  const onSelectImage: ChangeEventHandler<HTMLInputElement> = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setImageSrc(URL.createObjectURL(file));
    }
  };
  const resetField = () => {
    setSelectedFile(undefined);
    setImageSrc(undefined);
  };
  const uploadImage = async () => {
    if (selectedFile) {
      const formData = new FormData();
      formData.append('file', selectedFile, selectedFile.name);
      formData.append('folderName', folderName);
      formData.append('fileType', 'image');
      const response = await upsertMediaAsset({ mediaAssetId, data: formData });
      setSelectedFile(undefined);
      setImageSrc(response.data.fileURL);
      return response.data;
    }
  };

  if (imageSrc) {
    return (
      <Card className={cn('p-0 gap-0 overflow-hidden', width, className)}>
        <CardContent className="p-0 justify-center items-center flex">
          <img src={imageSrc} alt="Image" className={cn('object-cover', width, height)} />
        </CardContent>
        <CardFooter className="justify-between p-2 gap-2">
          <div className="flex-1 overflow-hidden text-ellipsis">{selectedFile?.name}</div>
          <label className="flex cursor-pointer gap-0.5">
            <ImageIcon strokeWidth="1" fill="red" color="white" className="size-6" />
            Change
            <input accept="image/*" type="file" className="hidden" onChange={onSelectImage} />
          </label>
        </CardFooter>
      </Card>
    );
  }
  return (
    <label
      className={cn(
        width,
        height,
        'flex  flex-col items-center justify-center bg-gray-100 cursor-pointer border-2 border-gray-300 border-dashed',
        className,
      )}
    >
      <ImageIcon className="size-40 text-gray-400" strokeWidth="0.5" />
      <input accept="image/*" type="file" className="hidden" onChange={onSelectImage} />
      <p className="text-gray-600">Select an image</p>
    </label>
  );
});

export default SingleImageFormField;
