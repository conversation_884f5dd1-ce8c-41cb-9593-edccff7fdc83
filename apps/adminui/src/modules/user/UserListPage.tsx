import useDocumentTitle from '@adminui/hooks/useDocumentTitle';
import MainLayout from '@adminui/layouts/MainLayout';
import React, { useState } from 'react';
import { useGetUserList } from './services/user-service';
import { Button } from '@adminui/components/ui/button';
import { RefreshCcw } from 'lucide-react';
import LinkButton from '@adminui/components/buttons/LinkButton';
import { Card, CardContent, CardFooter, CardHeader } from '@adminui/components/ui/card';
import SearchInput from '@adminui/components/common/SearchInput';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@adminui/components/ui/table';
import Spinner from '@adminui/components/common/Spinner';
import PageJump from '@adminui/components/common/PageJump';
import ListPagination from '@adminui/components/common/ListPagination';
import ActionMenu from './components/ActionMenu';
import EditUserComponent from './components/EditUserComponent';
import UserOpunitComponent from './components/UserOpunitComponent';
import useCanAccess from '@adminui/hooks/canAccess';

const breadcrumbItems = [
  { label: 'Dashboard', href: '/' },
  { label: 'User List', isPage: true },
];

const UserListPage: React.FC = () => {
  useDocumentTitle('User List');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [editUserId, setEditUserId] = useState<number | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  const [showOpunitDrawer, setShowOpunitDrawer] = useState(false);
  const [opunitUserId, setOpunitUserId] = useState<number | null>(null);

  const { data: response, isLoading, refetch } = useGetUserList({ page: currentPage, keyword: searchTerm, limit: 15 });

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleSearchEnterPress = () => {
    setCurrentPage(1);
    refetch();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    refetch();
  };

  const handleRefresh = () => {
    setCurrentPage(1);
    setSearchTerm('');
    refetch();
  };

  const handlePageJump = (page: number) => {
    setCurrentPage(page);
    refetch();
  };

  const handleEditClick = (userId: number) => {
    setEditUserId(userId);
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setEditUserId(null);
    setShowEditModal(false);
  };

  const handleOpunitOpen = (userId: number) => {
    setOpunitUserId(userId);
    setShowOpunitDrawer(true);
  };

  const handleOpunitClose = () => {
    setOpunitUserId(null);
    setShowOpunitDrawer(false);
  };

  const canManageUsers = useCanAccess({
    module: 'usermgmt',
    permissions: ['MANAGE_USER'],
  });
  const showUserAction = useCanAccess({
    module: 'usermgmt',
    permissions: ['MANAGE_USER', 'MANAGE_USER_OP_UNIT'],
  });

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page px-2 py-2">
        <div className="top-bar flex justify-between items-center px-4 py-2 bg-white rounded-md shadow-md">
          <div className="left-side text-xl font-semibold text-gray-700">User List</div>
          <div className="right-side">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size={'icon'} onClick={handleRefresh}>
                <RefreshCcw />
              </Button>
              {canManageUsers && <LinkButton href="/users/create" label="Add User" />}
            </div>
          </div>
        </div>
        <div className="table-section mt-2">
          <Card className="rounded-md shadow-md gap-1 py-2 px-2">
            <CardHeader className="py-2 gap-0.5">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                <SearchInput
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  onEnterPress={handleSearchEnterPress}
                />
              </div>
            </CardHeader>
            <CardContent className="p-0 m-0">
              <div className="table-container py-2">
                <Table className="border shadow-sm">
                  <TableHeader>
                    <TableRow>
                      <TableHead>#</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Mobile</TableHead>
                      <TableHead className="text-center">Role</TableHead>
                      <TableHead className="text-center">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading && (
                      <TableRow>
                        <TableCell colSpan={6}>
                          <div className="flex items-center justify-center h-32">
                            <Spinner size={'sm'} color="blue-500" />
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                    {response?.data.items.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center h-32">
                          No Records Found
                        </TableCell>
                      </TableRow>
                    )}
                    {response?.data?.items?.map((e: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{(currentPage - 1) * response.data.limit + index + 1}</TableCell>
                        <TableCell>{e.name}</TableCell>
                        <TableCell>{e.emailId ? e.emailId : '--'}</TableCell>
                        <TableCell>{e.mobileNo ? e.mobileNo : '--'}</TableCell>
                        <TableCell className="text-center">{e.role ? e.role.name : '--'}</TableCell>
                        <TableCell>
                          {showUserAction && (
                            <ActionMenu dataId={e.id} onEditClick={handleEditClick} onOpunitOpen={handleOpunitOpen} />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            {response && (
              <CardFooter className="flex flex-col md:flex-row justify-between items-center py-2">
                <div className="page-jumper mb-2 md:mb-0 w-[1/3]">
                  <PageJump pageNumber={currentPage} handlePageJump={handlePageJump} />
                </div>
                <div className="page-count mb-2 md:mb-0 w-[2/3]">
                  <ListPagination
                    currentPage={currentPage}
                    firstPage={1}
                    totalPages={Math.ceil(response?.data.total / response?.data.limit)}
                    onPageChange={handlePageChange}
                  />
                </div>
              </CardFooter>
            )}
          </Card>
        </div>
      </section>
      {showEditModal && <EditUserComponent userId={editUserId} onClose={handleCloseEditModal} />}
      {showOpunitDrawer && <UserOpunitComponent userId={String(opunitUserId)} onClose={handleOpunitClose} />}
    </MainLayout>
  );
};

export default UserListPage;
