import React, { useMemo } from 'react';
import { useAddOpunitToUser, useGetOpunitList, useRoles } from '../services/user-service';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardFooter } from '@adminui/components/ui/card';
import { Form } from '@adminui/components/ui/form';
import SearchableSelectInput from '@adminui/components/form-inputs/SearchableSelectInput';
import SubmitButton from '@adminui/components/buttons/SubmitButton';
import { Button } from '@adminui/components/ui/button';
import { parseErrorResponse } from '@adminui/lib/api-error-parser';
import { toast } from 'react-toastify';

interface Props {
  userId: string;
  cancelCreate: () => void;
}

const validationSchema = z
  .object({
    opunitId: z.string(),
    userId: z.string(),
    roleId: z.string(),
  })
  .refine((data) => new Set([data.opunitId, data.userId, data.roleId]).size === 3, {
    message: 'All fields (opunitId, userId, roleId) must be unique',
    path: ['opunitId', 'userId', 'roleId'],
  });
type formValues = z.infer<typeof validationSchema>;
const AddOpunitToUserComponent: React.FC<Props> = ({ userId, cancelCreate }) => {
  const [searchRoleTerm, setSearchRoleTerm] = React.useState('');

  const {
    data: roleList,
    isLoading: isRolesLoading,
    refetch: refetchRole,
  } = useRoles({
    page: 1,
    keyword: searchRoleTerm,
    limit: 15,
  });
  const roleOptions: { label: string; value: string }[] = [];
  if (!isRolesLoading) {
    roleOptions.push(
      ...(roleList?.data?.items ?? []).map((role: any) => ({
        label: role.name,
        value: String(role.id),
      })),
    );
  }

  const handleRoleSearch = (value: string) => {
    setSearchRoleTerm(value);
    refetchRole();
  };

  const [searchOpunitTerm, setSearchOpunitTerm] = React.useState('');

  const opunitOptions: { label: string; value: string }[] = [];
  const {
    data: opunitList,
    isLoading: isOpunitLoading,
    refetch: refetchOpunit,
  } = useGetOpunitList({
    page: 1,
    keyword: searchOpunitTerm,
    limit: 15,
  });
  if (!isOpunitLoading) {
    opunitOptions.push(
      ...(opunitList?.data?.items ?? []).map((opunit: any) => ({
        label: opunit.name,
        value: String(opunit.id),
      })),
    );
  }
  const handleOpunitSearch = (value: string) => {
    setSearchOpunitTerm(value);
    refetchOpunit();
  };

  const defaultValues = useMemo(
    () => ({
      opunitId: '',
      userId: userId,
      roleId: '',
    }),
    [userId],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError } = form;

  const { mutateAsync: addOpunit, isPending: loading } = useAddOpunitToUser();

  const onSubmit = (data: formValues) => {
    const parsedData = {
      ...data,
      opunitId: Number(data.opunitId),
      userId: Number(data.userId),
      roleId: Number(data.roleId),
    };
    console.log(parsedData);
    addOpunit(parsedData, {
      onSuccess: () => {
        toast.success('Opunit added successfully');
        cancelCreate();
      },
      onError: (error: any) => {
        const { message, responseBody } = parseErrorResponse(error);
        toast.error(message);
        console.log(responseBody);
        if (responseBody?.message && Array.isArray(responseBody.message)) {
          responseBody.message.forEach((fieldError: any) => {
            const { property, errors } = fieldError;
            if (property && errors && Array.isArray(errors)) {
              setError(property, { message: errors.join(', ') });
            }
          });
        }
      },
    });
  };

  return (
    <div className="content mt-2">
      <Card className="rounded-md shadow-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 gap-2">
                <div className="flex flex-col space-y-1 min-h-[80px]">
                  <SearchableSelectInput
                    control={form.control}
                    name="opunitId"
                    label="Opunit"
                    placeholder="Select an opunit"
                    options={opunitOptions}
                    onSearch={handleOpunitSearch}
                    isLoading={isOpunitLoading}
                    isRequired
                  />
                </div>
                <div className="flex flex-col space-y-1 min-h-[80px]">
                  <SearchableSelectInput
                    control={form.control}
                    name="roleId"
                    label="Role"
                    placeholder="Select a role"
                    options={roleOptions}
                    onSearch={handleRoleSearch}
                    isLoading={isRolesLoading}
                    isRequired
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-4 p-4 bg-gray-50 border-t">
              <Button type="button" variant="outline" onClick={cancelCreate} className="px-6">
                Cancel
              </Button>
              <SubmitButton label="Save User" isLoading={loading} className="px-6" />
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
};

export default AddOpunitToUserComponent;
