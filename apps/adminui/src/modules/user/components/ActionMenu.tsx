import { Button } from '@adminui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@adminui/components/ui/dropdown-menu';
import useCanAccess from '@adminui/hooks/canAccess';
import { EditIcon, Store } from 'lucide-react';
import React from 'react';

type Props = {
  dataId: number;
  onEditClick: (dataId: number) => void;
  onOpunitOpen: (dataId: number) => void;
};

const ActionMenu: React.FC<Props> = ({ dataId, onEditClick, onOpunitOpen }) => {
  const handleEditClick = () => {
    onEditClick(dataId);
  };

  const openOpunitDrawer = () => {
    onOpunitOpen(dataId);
  };
  const canManageUsers = useCanAccess({
    module: 'usermgmt',
    permissions: ['MANAGE_USER'],
  });

  const canManageUsersOpunit = useCanAccess({
    module: 'usermgmt',
    permissions: ['MANAGE_USER_OP_UNIT'],
  });

  return (
    <div className="flex items-center justify-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size={'sm'}>Action</Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {canManageUsers && (
            <DropdownMenuItem onClick={handleEditClick}>
              <EditIcon className="mr-2 h-4 w-4" />
              <span>Edit</span>
            </DropdownMenuItem>
          )}
          {canManageUsersOpunit && (
            <DropdownMenuItem onClick={openOpunitDrawer}>
              <Store className="mr-2 h-4 w-4" />
              <span>Opunits</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ActionMenu;
