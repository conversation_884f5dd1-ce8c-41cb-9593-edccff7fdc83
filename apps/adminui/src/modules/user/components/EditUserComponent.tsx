import { Button } from '@adminui/components/ui/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from '@adminui/components/ui/drawer';
import React, { useEffect, useMemo } from 'react';
import { useGetUserById, useRoles, useUpdateUser } from '../services/user-service';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardFooter } from '@adminui/components/ui/card';
import { Form } from '@adminui/components/ui/form';
import SearchableSelectInput from '@adminui/components/form-inputs/SearchableSelectInput';
import TextInput from '@adminui/components/form-inputs/TextInput';
import PasswordInput from '@adminui/components/form-inputs/PasswordInput';
import EmailInput from '@adminui/components/form-inputs/EmailInput';
import SubmitButton from '@adminui/components/buttons/SubmitButton';
import { parseErrorResponse } from '@adminui/lib/api-error-parser';
import { toast } from 'react-toastify';
import Spinner from '@adminui/components/common/Spinner';

interface Props {
  userId?: number | null;
  onClose: () => void;
}

const validationSchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    emailId: z.string().optional(),
    mobileNo: z.string().optional(),
    password: z
      .string()
      .optional()
      .refine((val) => !val || val.length >= 6, {
        message: 'Password must be at least 6 characters',
      }),
    password_confirmation: z.string().optional(),
    role: z.string().nullable(),
  })
  .refine((data) => data.emailId || data.mobileNo, {
    message: 'Either email or mobile number is required.',
    path: ['emailId'],
  })
  .superRefine((data, context) => {
    // Validate email format if emailId is provided
    if (data.emailId && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.emailId)) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid email address.',
        path: ['emailId'],
      });
    }

    // Validate mobile number format if mobileNo is provided
    if (data.mobileNo && !/^\+\d{8,20}$/.test(data.mobileNo)) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid mobile number (8-20 digits).',
        path: ['mobileNo'],
      });
    }
    if (data.password !== data.password_confirmation) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Passwords do not match.',
        path: ['password_confirmation'],
      });
    }
  });

type formValues = z.infer<typeof validationSchema>;

const EditUserComponent: React.FC<Props> = ({ userId = null, onClose }) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [roleOptions, setRoleOptions] = React.useState<{ label: string; value: string }[]>([]);

  const {
    data: roleList,
    isLoading: isRolesLoading,
    refetch,
  } = useRoles({
    page: 1,
    keyword: searchTerm,
    limit: 15,
  });

  const { data: userData, isLoading: isUserLoading } = useGetUserById(userId?.toString() || '');

  const handleRoleSearch = (value: string) => {
    setSearchTerm(value);
    refetch();
  };

  // Update role options whenever roles or user data changes
  useEffect(() => {
    const fetchedRoles =
      roleList?.data?.items.map((role: any) => ({
        label: role.name,
        value: String(role.id),
      })) ?? [];

    // Check if the selected role is already in the fetched roles
    const selectedRole = userData?.data?.role && {
      label: userData.data.role.name,
      value: String(userData.data.role.id),
    };

    // Add the selected role if it's not in the fetched roles
    if (selectedRole && !fetchedRoles.some((role: any) => role.value === selectedRole.value)) {
      setRoleOptions([...fetchedRoles, selectedRole]);
    } else {
      setRoleOptions(fetchedRoles);
    }
  }, [roleList, userData]);

  const defaultValues = useMemo(
    () => ({
      name: '',
      emailId: '',
      mobileNo: '',
      password: '',
      password_confirmation: '',
      role: '',
    }),
    [],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError, setValue } = form;

  useEffect(() => {
    if (userData) {
      setValue('name', userData.data.name);
      setValue('emailId', userData.data.emailId ?? '');
      setValue('mobileNo', userData.data.mobileNo ?? '');
      setValue('role', userData.data.role ? String(userData.data.role?.id) : '');
    }
  }, [userData, setValue]);

  const { mutateAsync: updateUser, isPending: loading } = useUpdateUser();

  const onSubmit = (data: formValues) => {
    updateUser(
      { data, id: String(userId) },
      {
        onSuccess: (data) => {
          console.log(data);
          toast.success('User created successfully');
        },
        onError: (error) => {
          const { message, responseBody } = parseErrorResponse(error);
          toast.error(message);
          console.log(responseBody);
          if (responseBody?.message && Array.isArray(responseBody.message)) {
            responseBody.message.forEach((fieldError: any) => {
              const { property, errors } = fieldError;
              if (property && errors && Array.isArray(errors)) {
                setError(property, { message: errors.join(', ') });
              }
            });
          }
        },
      },
    );
  };

  return (
    <React.Fragment>
      <Drawer open={!!userId} onOpenChange={(isOpen) => !isOpen && onClose()} direction="right" autoFocus={!!userId}>
        <DrawerContent>
          <DrawerHeader className="border-b shadow-md">
            <DrawerTitle>Edit User</DrawerTitle>
            <DrawerDescription>Modify the details of the user below.</DrawerDescription>
          </DrawerHeader>
          <div className="content mt-2 px-2 max-h-screen overflow-y-auto">
            {isUserLoading || isRolesLoading ? (
              <div className="flex items-center justify-center h-32">
                <Spinner size={'sm'} color="blue-500" />
              </div>
            ) : (
              <Card className="rounded-md shadow-md">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)}>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 gap-1">
                        <div className="flex flex-col space-y-1 min-h-[80px]">
                          <TextInput
                            control={form.control}
                            name="name"
                            label="Name"
                            placeholder="Enter your name"
                            isRequired
                          />
                        </div>
                        <div className="flex flex-col space-y-1 min-h-[80px]">
                          <EmailInput
                            control={form.control}
                            name="emailId"
                            label="Email"
                            placeholder="Enter your email"
                          />
                        </div>
                        <div className="flex flex-col space-y-1 min-h-[80px]">
                          <TextInput
                            control={form.control}
                            name="mobileNo"
                            label="Mobile Number"
                            placeholder="Enter your mobile number"
                          />
                        </div>
                        <div className="flex flex-col space-y-1 min-h-[80px]">
                          <PasswordInput
                            control={form.control}
                            name="password"
                            label="Password"
                            placeholder="Enter your password"
                          />
                        </div>
                        <div className="flex flex-col space-y-1 min-h-[80px]">
                          <TextInput
                            control={form.control}
                            name="password_confirmation"
                            label="Confirm Password"
                            placeholder="Confirm your password"
                          />
                        </div>
                        <div className="flex flex-col space-y-1 min-h-[80px]">
                          <SearchableSelectInput
                            control={form.control}
                            name="role"
                            label="Role"
                            placeholder="Select a role"
                            options={roleOptions}
                            isLoading={isRolesLoading}
                            onSearch={handleRoleSearch}
                          />
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end gap-4 p-4 bg-gray-50 border-t">
                      <Button type="button" variant="outline" onClick={onClose} className="px-6">
                        Cancel
                      </Button>
                      <SubmitButton label="Save User" isLoading={loading} className="px-6" />
                    </CardFooter>
                  </form>
                </Form>
              </Card>
            )}
          </div>
        </DrawerContent>
      </Drawer>
    </React.Fragment>
  );
};

export default EditUserComponent;
