import React from 'react';
import { useGetOpunitListByUserId, useRemoveOpunitFromUser } from '../services/user-service';
import { Button } from '@adminui/components/ui/button';
import { X } from 'lucide-react';
import Spinner from '@adminui/components/common/Spinner';
import { useConfirmDialog } from '@adminui/hooks/use-confirm-dialog';
import { toast } from 'react-toastify';

interface Props {
  userId: string;
  addOpunit: () => void;
}

const UserOpunitListComponent: React.FC<Props> = ({ userId, addOpunit }) => {
  const { showConfirmDialog, ConfirmDialogComponent } = useConfirmDialog();
  const { data: response, isLoading: isFetching, refetch } = useGetOpunitListByUserId(userId);

  React.useEffect(() => {
    refetch();
  }, [refetch]);

  const { mutateAsync: handleDelete } = useRemoveOpunitFromUser();

  const handelRemoveOpunit = (opunitId: string) => {
    showConfirmDialog({
      title: 'Are you sure?',
      description: 'This action cannot be undone.',
      confirmText: 'Remove',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          handleDelete(opunitId);
          toast.success('Opunit removed successfully');
        } catch (error) {
          console.error('Error removing opunit:', error);
          toast.error('Failed to remove opunit');
        }
      },
    });
  };

  return (
    <div className="p-4 bg-white shadow rounded-md w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Operational Units</h2>
        <Button onClick={addOpunit} size="sm" className="bg-blue-500 text-white hover:bg-blue-600">
          Add Opunit
        </Button>
      </div>
      {isFetching && (
        <div className="flex items-center justify-center h-32">
          <Spinner size={'sm'} color="blue-500" />
        </div>
      )}
      {!isFetching && response?.data.length === 0 && (
        <div className="flex items-center justify-center h-32">
          <p className="text-gray-500">No opunits assigned</p>
        </div>
      )}
      {!isFetching && (
        <ul className="divide-y divide-gray-200">
          {response?.data.map((item: any, index: number) => (
            <li key={item.opunit.id + index} className="flex justify-between items-center py-4">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800">{item.opunit.name}</p>
                <p className="text-sm text-gray-500">Role Name: {item.role.name}</p>
                <p className="text-sm text-gray-500">Description: {item.role.description}</p>
              </div>
              <Button
                onClick={() => handelRemoveOpunit(item.id)}
                size="icon"
                variant="destructive"
                title="Remove this operational unit"
              >
                <X />
              </Button>
            </li>
          ))}
        </ul>
      )}
      {ConfirmDialogComponent}
    </div>
  );
};

export default UserOpunitListComponent;
