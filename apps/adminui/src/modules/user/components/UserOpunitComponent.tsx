import { Button } from '@adminui/components/ui/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from '@adminui/components/ui/drawer';
import React from 'react';
import UserOpunitListComponent from './UserOpunitListComponent';
import AddOpunitToUserComponent from './AddOpunitToUserComponent';
import { X } from 'lucide-react';

interface Props {
  userId: string;
  onClose: () => void;
}

const UserOpunitComponent: React.FC<Props> = ({ userId, onClose }) => {
  const [activeTab, setActiveTab] = React.useState<'LIST' | 'CREATE'>('LIST');

  const openAddOpunit = () => {
    setActiveTab('CREATE');
  };
  const showList = () => {
    setActiveTab('LIST');
  };

  return (
    <React.Fragment>
      <Drawer open={!!userId} onOpenChange={(isOpen) => !isOpen && onClose()} direction="right" autoFocus={!!userId}>
        <DrawerContent className="min-w-[350px]">
          <DrawerHeader className="border-b flex flex-row justify-between items-center">
            <div>
              <DrawerTitle>Opunits</DrawerTitle>
              <DrawerDescription>All opunits assigned to this User</DrawerDescription>
            </div>
            <Button size="icon" variant="destructive" onClick={onClose} title="Close Drawer">
              <X />
            </Button>
          </DrawerHeader>
          <div className="content mt-2 px-2 max-h-screen overflow-y-auto">
            {activeTab === 'LIST' && <UserOpunitListComponent userId={userId} addOpunit={openAddOpunit} />}
            {activeTab === 'CREATE' && <AddOpunitToUserComponent userId={userId} cancelCreate={showList} />}
          </div>
        </DrawerContent>
      </Drawer>
    </React.Fragment>
  );
};

export default UserOpunitComponent;
