import useDocumentTitle from '@adminui/hooks/useDocumentTitle';
import MainLayout from '@adminui/layouts/MainLayout';
import React, { useMemo } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateUser, useRoles } from './services/user-service';
import GoBackButton from '@adminui/components/buttons/GoBackButton';
import { Card, CardContent, CardFooter } from '@adminui/components/ui/card';
import { Form } from '@adminui/components/ui/form';
import TextInput from '@adminui/components/form-inputs/TextInput';
import EmailInput from '@adminui/components/form-inputs/EmailInput';
import PasswordInput from '@adminui/components/form-inputs/PasswordInput';
import SubmitButton from '@adminui/components/buttons/SubmitButton';
import SearchableSelectInput from '@adminui/components/form-inputs/SearchableSelectInput';
import { parseErrorResponse } from '@adminui/lib/api-error-parser';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';

const breadcrumbItems = [
  { label: 'Dashboard', href: '/' },
  { label: 'Users', href: '/users' },
  { label: 'Create', isPage: true },
];

const validationSchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    emailId: z.string().optional(),
    mobileNo: z.string().optional(),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    password_confirmation: z.string().min(1, 'Please confirm your password.'),
    role: z.string().nullable(),
  })
  .refine((data) => data.emailId || data.mobileNo, {
    message: 'Either email or mobile number is required.',
    path: ['emailId'],
  })
  .superRefine((data, context) => {
    // Validate email format if emailId is provided
    if (data.emailId && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.emailId)) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid email address.',
        path: ['emailId'],
      });
    }

    // Validate mobile number format if mobileNo is provided
    if (data.mobileNo && !/^\+\d{8,20}$/.test(data.mobileNo)) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid mobile number (8-20 digits).',
        path: ['mobileNo'],
      });
    }
    if (data.password !== data.password_confirmation) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Passwords do not match.',
        path: ['password_confirmation'],
      });
    }
  });

type formValues = z.infer<typeof validationSchema>;
const UserCreatePage: React.FC = () => {
  useDocumentTitle('User Create');
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = React.useState('');

  const {
    data: roleList,
    isLoading: isRolesLoading,
    refetch,
  } = useRoles({
    page: 1,
    keyword: searchTerm,
    limit: 15,
  });
  const roleOptions: { label: string; value: string }[] = [];
  if (!isRolesLoading) {
    roleOptions.push(
      ...(roleList?.data?.items ?? []).map((role: any) => ({
        label: role.name,
        value: String(role.id),
      })),
    );
  }

  const handleRoleSearch = (value: string) => {
    setSearchTerm(value);
    refetch();
  };

  const defaultValues = useMemo(
    () => ({
      name: '',
      emailId: '',
      mobileNo: '',
      password: '',
      password_confirmation: '',
      role: '',
    }),
    [],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError } = form;

  const { mutateAsync: createUser, isPending: loading } = useCreateUser();

  const onSubmit = (data: formValues) => {
    createUser(data, {
      onSuccess: (data) => {
        console.log(data);
        toast.success('User created successfully');
        navigate('/users');
      },
      onError: (error) => {
        const { message, responseBody } = parseErrorResponse(error);
        toast.error(message);
        console.log(responseBody);
        if (responseBody?.message && Array.isArray(responseBody.message)) {
          responseBody.message.forEach((fieldError: any) => {
            const { property, errors } = fieldError;
            if (property && errors && Array.isArray(errors)) {
              setError(property, { message: errors.join(', ') });
            }
          });
        }
      },
    });
  };

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page px-2 py-2">
        <div className="top-bar flex justify-between items-center px-4 py-2 bg-white rounded-md shadow-md">
          <div className="left-side text-xl font-semibold text-gray-700">Create User</div>
          <div className="right-side">
            <div className="flex items-center gap-2">
              <GoBackButton href="/users" label="Go Back" />
            </div>
          </div>
        </div>
        <div className="content mt-2">
          <Card className="rounded-md shadow-md">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextInput
                        control={form.control}
                        name="name"
                        label="Name"
                        placeholder="Enter your name"
                        isRequired
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <EmailInput control={form.control} name="emailId" label="Email" placeholder="Enter your email" />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextInput
                        control={form.control}
                        name="mobileNo"
                        label="Mobile Number"
                        placeholder="Enter your mobile number"
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <PasswordInput
                        control={form.control}
                        name="password"
                        label="Password"
                        placeholder="Enter your password"
                        isRequired
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextInput
                        control={form.control}
                        name="password_confirmation"
                        label="Confirm Password"
                        placeholder="Confirm your password"
                        isRequired
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <SearchableSelectInput
                        control={form.control}
                        name="role"
                        label="Role"
                        placeholder="Select a role"
                        options={roleOptions}
                        onSearch={handleRoleSearch}
                        isLoading={isRolesLoading}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end p-4">
                  <SubmitButton label="Create User" isLoading={loading} />
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </section>
    </MainLayout>
  );
};

export default UserCreatePage;
