import { apiClient } from '@adminui/lib/api-client';
import { ApiResponseType } from '@common-utils/api-types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

type UserParams = {
  keyword?: string;
  page?: number;
  limit?: number; // Changed to number for consistency
};

type UserListResponse = {
  items: User[];
  total: number;
  page: number;
  limit: number;
};

export type User = {
  id: number;
  name: string;
  emailId: string;
  mobileNo: string;
  avatar: string | null;
  role?: any | null;
};

type RoleParams = {
  keyword?: string;
  page?: number;
  limit?: number; // Changed to number for consistency
};
// Get User List with Pagination
export const useGetUserList = (userParams: UserParams) => {
  const { keyword, page, limit } = userParams;
  const params = { page, keyword, limit };

  return useQuery({
    queryKey: ['user-list', params],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<UserListResponse>>('/users', {
        params,
      });
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

// Get User By Id
export const useGetUserById = (id: string) => {
  return useQuery({
    queryKey: ['user-by-id', id],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>(`/users/${id}`);
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

//Get Roles
export const useRoles = (reqParams: RoleParams) => {
  const { keyword, page, limit } = reqParams;
  const params = { page, keyword, limit };
  return useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>('/roles', {
        params,
      });
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

//Create User
export const useCreateUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const { data: response } = await apiClient.post<ApiResponseType<any>>('/users', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-list'] });
    },
  });
};

// Update User
export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ data, id }: { data: any; id: string }) => {
      const { data: response } = await apiClient.put<ApiResponseType<any>>(`/users/${id}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-list'] });
    },
  });
};

//Get Opunit List By User Id
export const useGetOpunitListByUserId = (id: string) => {
  return useQuery({
    queryKey: ['opunit-list-by-user-id', id],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>(`/users/${id}/get-opunits`);
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

//Get All Opunit List

export const useGetOpunitList = (reqParams: RoleParams) => {
  const { keyword, page, limit } = reqParams;
  const params = { page, keyword, limit };
  return useQuery({
    queryKey: ['get-all-opunits'],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>('/operational-units', {
        params,
      });
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

// Add Opunit To User
export const useAddOpunitToUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const { data: response } = await apiClient.post<ApiResponseType<any>>('/users/add-opunit', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opunit-list-by-user-id'] });
    },
  });
};
// Remove Opunit From User
export const useRemoveOpunitFromUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const { data: response } = await apiClient.delete<ApiResponseType<any>>(`/users/remove-opunit/${id}`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opunit-list-by-user-id'] });
    },
  });
};
