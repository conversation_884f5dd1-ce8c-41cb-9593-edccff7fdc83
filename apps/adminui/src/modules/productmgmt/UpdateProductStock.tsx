import { Button } from '@adminui/components/ui/button';
import MainLayout from '@adminui/layouts/MainLayout';
import { SaveIcon } from 'lucide-react';
import { useGetOpunitList } from '../opunit/services/opunit-service';
import { TableHeader, TableRow, TableHead, TableBody, TableCell, Table } from '@adminui/components/ui/table';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Products', href: '/products' },
  { label: 'Update stock', isPage: true },
];

const UpdateProductStock = () => {
  const { productSlug } = useParams();
  const { data: productDetails } = useBookDetails(productSlug);
  const { data: opunits, isLoading } = useGetOpunitList({ limit: 15, page: 1 });
  return (
    <MainLayout breadcrumbItems={breadcrumbItems} className="px-8" isLoading={isLoading}>
      <div className="flex justify-between sticky top-0 bg-white z-10  py-4">
        <div>
          <h1 className="text-xl">Update initial product stock for {productDetails?.title}</h1>
        </div>
        <div className="flex gap-4">
          <Button>
            <SaveIcon className="size-6" /> Save
          </Button>
        </div>
      </div>
      <Table className="mt-4 relative">
        <TableHeader className="sticky top-0">
          <TableRow>
            <TableHead>Operational Unit Name</TableHead>
            <TableHead>Stock</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {opunits?.data.items.map((opunit) => (
            <TableRow key={opunit.id}>
              <TableCell>{opunit.name}</TableCell>
              <TableCell>
                <input type="text" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </MainLayout>
  );
};

export default UpdateProductStock;

const useBookDetails = (slug?: string) => {
  return useQuery({
    queryKey: ['Existing_Books_Details'],
    queryFn: () =>
      fetch(`https://api.deyspublishing.com/api/books-details/${slug}`)
        .then((d) => d.json())
        .then((d) => d.data.book as { title: string }),
    enabled: Boolean(slug),
  });
};
