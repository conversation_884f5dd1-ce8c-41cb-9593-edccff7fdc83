import { Button } from '@adminui/components/ui/button';
import { useProductTypes } from '../service/product-type.service';
import { Link } from 'react-router-dom';
import { PlusIcon } from 'lucide-react';

const ProductListHeader = () => {
  const { data: productTypes = [] } = useProductTypes();
  return (
    <div className="flex justify-between sticky top-0 bg-white z-10  py-4">
      <div>
        <h1 className="text-xl">Product List</h1>
      </div>
      <div className="flex gap-4">
        <Button variant="outline">Upload Product</Button>
        {productTypes.map((productType) => (
          <Button asChild key={productType.id}>
            <Link to={`/products/add-new/${productType.id}`} className="button">
              <PlusIcon className="size-6" />
              {`Add New ${productType.name}`}
            </Link>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default ProductListHeader;
