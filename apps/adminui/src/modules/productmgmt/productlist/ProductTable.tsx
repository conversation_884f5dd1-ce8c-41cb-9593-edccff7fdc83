import { TableHeader, TableRow, TableHead, TableBody, Table, TableCell } from '@adminui/components/ui/table';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';

const ProductTable = () => {
  const { data: productData = [] } = useBooks();
  return (
    <Table className="mt-4 relative">
      <TableHeader className="sticky top-0">
        <TableRow>
          <TableHead>Product</TableHead>
          <TableHead>SKU Code</TableHead>
          <TableHead>Stock</TableHead>
          <TableHead>Category</TableHead>
          <TableHead className="text-center">Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {productData.map((product) => (
          <TableRow key={product.id}>
            <TableCell>{product.title}</TableCell>
            <TableCell>{product.book_variants?.[0]?.sku}</TableCell>
            <TableCell>
              <Link to={`/products/update-product-stock/${product.book_slug}`}>
                {product.book_variants?.[0]?.stock}
              </Link>
            </TableCell>
            <TableCell>{product.categories?.[0]?.category_name}</TableCell>
            <TableCell className="text-center">Action</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default ProductTable;

type ExP = {
  id: number;
  title: string;
  book_slug: string;
  book_variants: {
    sku: string;
    stock: number;
  }[];
  categories: {
    category_name: string;
  }[];
};
const useBooks = () => {
  return useQuery({
    queryKey: ['Existing_Books'],
    queryFn: () =>
      fetch(`https://api.deyspublishing.com/api/books-list?limit=100`)
        .then((d) => d.json())
        .then((d) => d.data.data as ExP[]),
  });
};
