import MainLayout from '@adminui/layouts/MainLayout';
import ProductListHeader from './ProductListHeader';
import ProductTable from './ProductTable';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Products', isPage: true },
];

const ProductList = () => {
  return (
    <MainLayout breadcrumbItems={breadcrumbItems} className="px-8">
      <ProductListHeader />
      <ProductTable />
    </MainLayout>
  );
};

export default ProductList;
