import { apiClient } from '@adminui/lib/api-client';
import { ApiResponseType } from '@common-utils/api-types';
import { useQuery } from '@tanstack/react-query';

export type ProductTypeAttribute = {
  id: number;
  name: string;
  dataType: string;
  allowMultiple: boolean;
  isRequired: boolean;
  isVariantAttribute: boolean;
  isVariantSelector: boolean;
};
export type ProductTypeBasic = {
  id: number;
  name: string;
  description?: string;
};
export type ProductTypeDetails = ProductTypeBasic & {
  attributes: ProductTypeAttribute[];
};

export const useProductTypes = () => {
  return useQuery({
    queryKey: ['product-types'],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<ProductTypeBasic[]>>('/product-types');
      return data;
    },
    staleTime: Infinity,
    retry: false,
    select: ({ data }) => data,
  });
};

export const useProductTypeDetails = (productTypeId?: string) => {
  return useQuery({
    queryKey: ['product-type-details', productTypeId],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<ProductTypeDetails>>(`/product-types/${productTypeId}`);
      return data;
    },
    staleTime: 5000,
    retry: false,
    select: ({ data }) => data,
    enabled: Boolean(productTypeId),
  });
};
