import MainLayout from '@adminui/layouts/MainLayout';
import { useParams } from 'react-router-dom';
import { useProductTypeDetails } from '../service/product-type.service';
import ProductForm from './ProductForm';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Products', href: '/products' },
  { label: 'Add New', isPage: true },
];

const AddProduct = () => {
  const { productTypeId } = useParams();
  const { data: productTypeDetails, isLoading } = useProductTypeDetails(productTypeId);

  return (
    <MainLayout breadcrumbItems={breadcrumbItems} className="px-8" isLoading={isLoading}>
      <div className="flex justify-between sticky top-0 bg-white z-10  py-4">
        <div>
          <h1 className="text-xl">Add New {productTypeDetails?.name || 'Product'}</h1>
        </div>
      </div>
      {productTypeDetails ? <ProductForm productTypeDetails={productTypeDetails} /> : null}
    </MainLayout>
  );
};

export default AddProduct;
