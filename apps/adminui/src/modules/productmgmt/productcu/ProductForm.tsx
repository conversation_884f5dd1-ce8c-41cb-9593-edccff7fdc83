import { Form } from '@adminui/components/ui/form';
import { ProductTypeDetails } from '../service/product-type.service';
import { useFieldArray, useForm } from 'react-hook-form';
import TextInput from '@adminui/components/form-inputs/TextInput';
import TextAreaInput from '@adminui/components/form-inputs/TextAreaInput';
import { useMemo } from 'react';

export type ProductFormProps = {
  productTypeDetails: ProductTypeDetails;
  defaultProductData?: ProductFormData;
};

const ProductForm = (props: ProductFormProps) => {
  const { defaultProductData, productTypeDetails } = props;
  const { productAttributes, productVariantAttributes } = useMemo(() => {
    const productAttributes = productTypeDetails.attributes.filter((attribute) => !attribute.isVariantAttribute);
    const productVariantAttributes = productTypeDetails.attributes.filter((attribute) => attribute.isVariantAttribute);
    return { productAttributes, productVariantAttributes };
  }, [productTypeDetails]);

  const form = useForm<ProductFormData>({
    defaultValues: defaultProductData,
  });
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'productVariations',
  });

  const onSubmit = (data: ProductFormData) => {};
  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-2 gap-8">
            <div className="flex flex-col gap-6">
              <TextInput control={form.control} name="name" label="Name" placeholder="Product Name" isRequired />
              <TextAreaInput
                control={form.control}
                name="description"
                label="Description"
                placeholder="Product Description"
              />
              <TextInput
                control={form.control}
                name="slug"
                label="Slug"
                placeholder="Unique product name. no space"
                isRequired
              />
              {productAttributes.map((attribute, index) => (
                <TextInput
                  control={form.control}
                  name={`productAttributes.${index}.${attribute.id}`}
                  label={attribute.name}
                  isRequired={attribute.isRequired}
                />
              ))}
            </div>
            <div>
              <h4 className="text-xl text-center"> Product Variants</h4>
              <div className="flex flex-col gap-6">
                {productVariantAttributes.map((attribute, index) => (
                  <TextInput
                    control={form.control}
                    name={`productAttributes.${index}.${attribute.id}`}
                    label={attribute.name}
                    isRequired={attribute.isRequired}
                  />
                ))}
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ProductForm;

export type ProductAttribute = {
  id?: number;
  productId?: number;
  attributeId: number;
  attributeValue: string;
};
export type ProductVariationAttribute = {
  id?: number;
  productVariationId?: number;
  attributeId: number;
  attributeValue: string;
};
export type ProductVariation = {
  id?: number;
  name: string;
  basePrice: number;
  skucode: string;
  isDefaultVariant: boolean;
  attributes: ProductVariationAttribute[];
};
export type ProductFormData = {
  name: string;
  description?: string;
  slug?: string;
  productAttributes: ProductAttribute[];
  productVariations: ProductVariation[];
};
