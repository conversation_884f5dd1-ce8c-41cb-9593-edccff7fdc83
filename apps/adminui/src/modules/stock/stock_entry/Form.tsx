import TextAreaInput from '@adminui/components/form-inputs/TextAreaInput';
import TextInput from '@adminui/components/form-inputs/TextInput';
import { Form } from '@adminui/components/ui/form';
import { Label } from '@adminui/components/ui/label';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@adminui/components/ui/table';

import { useFieldArray, useForm } from 'react-hook-form';

export type FormProps = {};

const StockEntryForm = (props: FormProps) => {
  const form = useForm({ values: { entryNo: 'ENT-13JUL25-xxxxx', items: [{ productVariationId: '', quantity: 0 }] } });
  const { fields: stockEntryItems, append } = useFieldArray({ control: form.control, name: 'items' });
  return (
    <div className="max-w-4xl  p-6 rounded">
      <Form {...form}>
        <form className="grid grid-cols-2 gap-8">
          <TextInput control={form.control} name="entryNo" label="Name" readOnly />
          <TextInput control={form.control} name="opunitId" label="Operational Unit" />
          <TextAreaInput
            control={form.control}
            name="description"
            label="Description/Notes"
            placeholder="Enter your address"
            className="col-span-2"
          />
          <div className="col-span-2">
            <Label>Items</Label>
            <Table className="mt-4">
              <TableHeader className="sticky top-0">
                <TableRow>
                  <TableHead className="p-0">Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead className="text-right">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stockEntryItems.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableHead className="p-0"></TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Action</TableHead>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default StockEntryForm;
