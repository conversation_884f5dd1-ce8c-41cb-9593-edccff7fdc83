import MainLayout from '@adminui/layouts/MainLayout';
import ListHeader from '@adminui/layouts/pagelayout/ListHeader';
import Form from './Form';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Stock Entries', href: '/stock/stock-entries' },
  { label: 'New', isPage: true },
];

const Create = () => {
  return (
    <MainLayout breadcrumbItems={breadcrumbItems} className="px-8">
      <ListHeader title="Create New Stock Entry" />
      <Form />
    </MainLayout>
  );
};

export default Create;
