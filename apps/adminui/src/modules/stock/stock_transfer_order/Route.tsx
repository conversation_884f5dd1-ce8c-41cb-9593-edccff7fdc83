import { NavLink } from 'react-router-dom';
import Update from './Update';
import Create from './Create';
import List from './List';
import { SidebarMenuItem, SidebarMenuButton } from '@adminui/components/ui/sidebar';
import { Users } from 'lucide-react';

const Route = [
  {
    path: '',
    element: <List />,
  },
  {
    path: 'create',
    element: <Create />,
  },
  {
    path: 'update/:identifier',
    element: <Update />,
  },
];

export default Route;

export const BASE_PATH = '/stock/stock-transfer-orders';
export const NAV_GROUP = 'Stock';

export const SidebarNav = (props: { isActive: boolean }) => (
  <SidebarMenuItem>
    <SidebarMenuButton asChild isActive={props.isActive}>
      <NavLink to={BASE_PATH}>
        <Users className="mr-2 h-4 w-4" />
        <span>Stock Transfer Orders</span>
      </NavLink>
    </SidebarMenuButton>
  </SidebarMenuItem>
);
