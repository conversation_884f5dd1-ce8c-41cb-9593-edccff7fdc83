import MainLayout from '@adminui/layouts/MainLayout';
import ListHeader from '@adminui/layouts/pagelayout/ListHeader';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Stock Adjustments', href: '/stock/stock-entries' },
  { label: 'New', isPage: true },
];

const Create = () => {
  return (
    <MainLayout breadcrumbItems={breadcrumbItems} className="px-8">
      <ListHeader title="Create New Stock Adjustment" />
    </MainLayout>
  );
};

export default Create;
