import { TableHeader, TableRow, TableHead, TableBody, Table } from '@adminui/components/ui/table';
import { dummyListData } from '@adminui/constants/config';
import MainLayout from '@adminui/layouts/MainLayout';
import ListHeader from '@adminui/layouts/pagelayout/ListHeader';

const breadcrumbItems = [
  { label: 'Home', href: '/' },
  { label: 'Stock Adjustments', isPage: true },
];

const List = () => {
  return (
    <MainLayout breadcrumbItems={breadcrumbItems} className="px-8">
      <ListHeader
        title="Stock Adjustments"
        addLinkButton={{ label: 'Create new stock adjustment', linkTo: '/stock/stock-adjustment/create' }}
      />
      <DataTable listData={dummyListData} />
    </MainLayout>
  );
};

export default List;

const DataTable = (props: { listData: Record<string, unknown>[] }) => {
  const { listData } = props;
  return (
    <Table className="mt-4 relative">
      <TableHeader className="sticky top-0">
        <TableRow>
          <TableHead>No.</TableHead>
          <TableHead>Description</TableHead>
          <TableHead>Operational Unit</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Executed On</TableHead>
          <TableHead className="text-center">Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {listData.map((item) => (
          <TableRow key={item.id as string}>
            <TableHead>ADJ-13JUL25-xxxxx</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Operational Unit</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Executed On</TableHead>
            <TableHead className="text-center">Action</TableHead>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
