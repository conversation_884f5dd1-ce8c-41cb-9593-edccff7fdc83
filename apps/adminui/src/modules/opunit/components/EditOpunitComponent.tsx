import React, { useEffect, useMemo } from 'react';
import { useGetOpunitById, useGetOpunitList, useUpdateOpunit } from '../services/opunit-service';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { parseErrorResponse } from '@adminui/lib/api-error-parser';
import { toast } from 'react-toastify';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from '@adminui/components/ui/drawer';
import Spinner from '@adminui/components/common/Spinner';
import { Card, CardContent, CardFooter } from '@adminui/components/ui/card';
import TextAreaInput from '@adminui/components/form-inputs/TextAreaInput';
import SearchableSelectInput from '@adminui/components/form-inputs/SearchableSelectInput';
import SubmitButton from '@adminui/components/buttons/SubmitButton';
import EmailInput from '@adminui/components/form-inputs/EmailInput';
import TextInput from '@adminui/components/form-inputs/TextInput';
import { Form } from '@adminui/components/ui/form';
import YesNoInput from '@adminui/components/form-inputs/YesNoInput';
import { Button } from '@adminui/components/ui/button';

interface Props {
  opunitId?: number | null;
  onClose: () => void;
}

const validationSchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    address: z.string().optional(),
    emailId: z
      .string()
      .optional()
      .refine((value) => {
        if (value) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        }
        return true;
      }, 'Please enter a valid email address.'),
    mobileNo: z
      .string()
      .optional()
      .refine((value) => {
        if (value) {
          return /^\+\d{8,20}$/.test(value);
        }
        return true;
      }, 'Please enter a valid mobile number.'),
    isInfiniteStock: z.boolean().optional(),
    proxyOpUnit: z.string().optional(),
    isActive: z.boolean(),
  })
  .refine((data) => !(data.isInfiniteStock && !data.proxyOpUnit), {
    message: 'Proxy Operational Unit is required when Infinite Stock is enabled.',
    path: ['proxyOpUnit'], // This points to the field that should show the error
  });

type formValues = z.infer<typeof validationSchema>;

const EditOpunitComponent: React.FC<Props> = ({ opunitId = null, onClose }) => {
  const [searchOpunitTerm, setSearchOpunitTerm] = React.useState('');
  const [proxyUnitOptions, setProxyUnitOptions] = React.useState<{ label: string; value: string }[]>([]);
  const {
    data: opunitList,
    isLoading: isOpunitLoading,
    refetch: refetchOpunit,
  } = useGetOpunitList({
    page: 1,
    keyword: searchOpunitTerm,
    limit: 15,
  });

  const { data: opunitData, isLoading: fetchingOpunit } = useGetOpunitById(opunitId?.toString() || '');
  // Update role options whenever roles or user data changes
  useEffect(() => {
    if (!opunitList || !opunitData) return;
    const fetchedProxyUnits =
      opunitList?.data?.items.map((role: any) => ({
        label: role.name,
        value: String(role.id),
      })) ?? [];

    // Check if the selected role is already in the fetched roles
    const selectedProxyUnit = opunitData.data.proxyOpUnit && {
      label: opunitData.data.proxyOpUnit.name,
      value: String(opunitData.data.proxyOpUnit.id),
    };

    // Add the selected role if it's not in the fetched roles
    if (selectedProxyUnit && !fetchedProxyUnits.some((role: any) => role.value === selectedProxyUnit.value)) {
      setProxyUnitOptions([...fetchedProxyUnits, selectedProxyUnit]);
    } else {
      setProxyUnitOptions(fetchedProxyUnits);
    }
  }, [opunitList, opunitData]);

  const handleOpunitSearch = (value: string) => {
    setSearchOpunitTerm(value);
    refetchOpunit();
  };

  const defaultValues = useMemo(
    () => ({
      name: '',
      address: '',
      emailId: '',
      mobileNo: '',
      isInfiniteStock: false,
      proxyOpUnit: '',
      isActive: true,
    }),
    [],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError, setValue } = form;

  useEffect(() => {
    if (opunitData) {
      setValue('name', opunitData.data.name);
      setValue('address', opunitData.data.address ?? '');
      setValue('emailId', opunitData.data.emailId ?? '');
      setValue('mobileNo', opunitData.data.mobileNo ?? '');
      setValue('isInfiniteStock', !!opunitData.data.isInfiniteStock);
      setValue('proxyOpUnit', opunitData.data.proxyOpUnit ? String(opunitData.data.proxyOpUnit?.id) : '');
      setValue('isActive', !!opunitData.data.isActive);
    }
  }, [opunitData, setValue]);

  const { mutateAsync: updateOpunit, isPending: loading } = useUpdateOpunit();

  const onSubmit = async (data: formValues) => {
    updateOpunit(
      { data, id: String(opunitId) },
      {
        onSuccess: () => {
          toast.success('Opunit updated successfully');
          onClose();
        },
        onError: (error: any) => {
          const { message, responseBody } = parseErrorResponse(error);
          toast.error(message);
          console.log(responseBody);
          if (responseBody?.message && Array.isArray(responseBody.message)) {
            responseBody.message.forEach((fieldError: any) => {
              const { property, errors } = fieldError;
              if (property && errors && Array.isArray(errors)) {
                setError(property, { message: errors.join(', ') });
              }
            });
          }
        },
      },
    );
  };

  return (
    <React.Fragment>
      <Drawer
        open={!!opunitId}
        onOpenChange={(isOpen) => !isOpen && onClose()}
        direction="right"
        autoFocus={!!opunitId}
      >
        <DrawerContent>
          <DrawerHeader className="border-b shadow-md">
            <DrawerTitle>Edit Operational Unit</DrawerTitle>
            <DrawerDescription>Modify the details of the operational unit below.</DrawerDescription>
          </DrawerHeader>
          <div className="content mt-2 px-2 max-h-screen overflow-y-auto">
            {isOpunitLoading || fetchingOpunit ? (
              <div className="flex items-center justify-center h-32">
                <Spinner size={'sm'} color="blue-500" />
              </div>
            ) : (
              <Card className="rounded-md shadow-md">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)}>
                    <CardContent className="p-4">
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <TextInput
                          control={form.control}
                          name="name"
                          label="Name"
                          placeholder="Enter your name"
                          isRequired
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <EmailInput
                          control={form.control}
                          name="emailId"
                          label="Email"
                          placeholder="Enter your email"
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <TextInput
                          control={form.control}
                          name="mobileNo"
                          label="Mobile Number"
                          placeholder="Enter your mobile number"
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <YesNoInput
                          control={form.control}
                          name="isActive"
                          label="Active Unit?"
                          placeholder="Operational Unit"
                          isRequired
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <YesNoInput
                          control={form.control}
                          name="isInfiniteStock"
                          label="Allow Infinite Stock"
                          placeholder="Infinite Stock"
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <SearchableSelectInput
                          control={form.control}
                          name="proxyOpUnit"
                          label="Proxy Operational Unit"
                          placeholder="Select a Operational Unit"
                          options={proxyUnitOptions}
                          onSearch={handleOpunitSearch}
                          isLoading={isOpunitLoading}
                        />
                      </div>
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <TextAreaInput
                          control={form.control}
                          name="address"
                          label="Address"
                          placeholder="Enter your address"
                        />
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end gap-4 p-4 bg-gray-50 border-t">
                      <Button type="button" variant="outline" onClick={onClose} className="px-6">
                        Cancel
                      </Button>
                      <SubmitButton label="Save Changes" isLoading={loading} className="px-6" />
                    </CardFooter>
                  </form>
                </Form>
              </Card>
            )}
          </div>
        </DrawerContent>
      </Drawer>
    </React.Fragment>
  );
};

export default EditOpunitComponent;
