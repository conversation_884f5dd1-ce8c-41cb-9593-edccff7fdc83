import { Button } from '@adminui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@adminui/components/ui/dropdown-menu';
import { CircleCheck, CircleX, EditIcon, Trash2 } from 'lucide-react';
import React from 'react';
import { useDeleteOpunit, useUpdateOpunitStatus } from '../services/opunit-service';
import { useConfirmDialog } from '@adminui/hooks/use-confirm-dialog';
import { toast } from 'react-toastify';

type Props = {
  dataId: number;
  status: boolean;
  onEditClick: (dataId: number) => void;
};

const ActionMenu: React.FC<Props> = ({ dataId, status, onEditClick }) => {
  const { showConfirmDialog, ConfirmDialogComponent } = useConfirmDialog();

  const handleEditClick = () => {
    onEditClick(dataId);
  };
  const { mutateAsync: handleDelete } = useDeleteOpunit();
  const handelRemoveOpunit = () => {
    showConfirmDialog({
      title: 'Are you absolutely sure?',
      description: 'This action cannot be undone. This will permanently delete the opunit.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          await handleDelete(String(dataId));
          toast.success('Opunit deleted successfully');
        } catch (error) {
          console.log(error);
          toast.error('Failed to delete opunit');
        }
      },
    });
  };

  const { mutateAsync: handleUpdateStatus } = useUpdateOpunitStatus();
  const handleUpdateStatusOpunit = () => {
    showConfirmDialog({
      title: 'Are you absolutely sure?',
      description: 'This action can be undone. This will temporarily update the opunit status.',
      confirmText: 'Update',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          await handleUpdateStatus({ id: String(dataId) });
          toast.success('Opunit disabled successfully');
        } catch (error) {
          console.log(error);
          toast.error('Failed to disable opunit');
        }
      },
    });
  };

  return (
    <div className="flex items-center justify-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size={'sm'}>Action</Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={handleEditClick}>
            <EditIcon className="mr-2 h-4 w-4" aria-hidden="true" />
            <span>Edit</span>
          </DropdownMenuItem>
          {status ? (
            <DropdownMenuItem onClick={handleUpdateStatusOpunit}>
              <CircleX className="mr-2 h-4 w-4" aria-hidden="true" />
              <span>Disable Opunit</span>
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem onClick={handleUpdateStatusOpunit}>
              <CircleCheck className="mr-2 h-4 w-4" aria-hidden="true" />
              <span>Enable Opunit</span>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem onClick={handelRemoveOpunit}>
            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
            <span>Remove</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {ConfirmDialogComponent}
    </div>
  );
};

export default ActionMenu;
