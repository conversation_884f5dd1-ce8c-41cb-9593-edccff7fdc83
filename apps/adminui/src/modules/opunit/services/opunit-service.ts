import { apiClient } from '@adminui/lib/api-client';
import { ApiResponseType } from '@common-utils/api-types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

type OpunitListParams = {
  keyword?: string;
  page?: number;
  limit?: number;
};

type OpunitListResponse = {
  items: Opunit[];
  total: number;
  page: number;
  limit: number;
};

type Opunit = {
  id: number;
  name: string;
  address: string | null;
  emailId: string | null;
  mobileNo: string | null;
  isActive: boolean;
  isInfiniteStock: boolean;
  proxyOpUnit?: any | null;
};

// Get Opunit List with Pagination
export const useGetOpunitList = (userParams: OpunitListParams) => {
  const { keyword, page, limit } = userParams;
  const params = { page, keyword, limit };

  return useQuery({
    queryKey: ['opunit-list', params],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<OpunitListResponse>>('/operational-units', {
        params,
      });
      return data; // Return only the `data` field
    },
    staleTime: 5000,
    retry: false,
  });
};

// Get Opunit By Id
export const useGetOpunitById = (id: string) => {
  return useQuery({
    queryKey: ['opunit-by-id', id],
    queryFn: async () => {
      const { data } = await apiClient.get<ApiResponseType<any>>(`/operational-units/${id}`);
      return data;
    },
    staleTime: 5000,
    retry: false,
  });
};

//Create Opunit
export const useCreateOpunit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: any) => {
      const { data: response } = await apiClient.post<ApiResponseType<any>>('/operational-units', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opunit-list'] });
    },
  });
};

// Update Opunit
export const useUpdateOpunit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ data, id }: { data: any; id: string }) => {
      const { data: response } = await apiClient.put<ApiResponseType<any>>(`/operational-units/${id}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opunit-list'] });
    },
  });
};

// Delete Opunit
export const useDeleteOpunit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const { data: response } = await apiClient.delete<ApiResponseType<any>>(`/operational-units/${id}`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opunit-list'] });
    },
  });
};

//Update Status Opunit
export const useUpdateOpunitStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      const { data: response } = await apiClient.put<ApiResponseType<any>>(`/operational-units/${id}/update-status`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opunit-list'] });
    },
  });
};
