import useDocumentTitle from '@adminui/hooks/useDocumentTitle';
import MainLayout from '@adminui/layouts/MainLayout';
import React, { useMemo } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { useCreateOpunit, useGetOpunitList } from './services/opunit-service';
import GoBackButton from '@adminui/components/buttons/GoBackButton';
import { Card, CardContent, CardFooter } from '@adminui/components/ui/card';
import { Form } from '@adminui/components/ui/form';
import TextInput from '@adminui/components/form-inputs/TextInput';
import EmailInput from '@adminui/components/form-inputs/EmailInput';
import SubmitButton from '@adminui/components/buttons/SubmitButton';
import SearchableSelectInput from '@adminui/components/form-inputs/SearchableSelectInput';
import YesNoInput from '@adminui/components/form-inputs/YesNoInput';
import TextAreaInput from '@adminui/components/form-inputs/TextAreaInput';

const breadcrumbItems = [
  { label: 'Dashboard', href: '/' },
  { label: 'Operational Units', href: '/opunits' },
  { label: 'Create', isPage: true },
];

const validationSchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    address: z.string().optional(),
    emailId: z
      .string()
      .optional()
      .refine((value) => {
        if (value) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        }
        return true;
      }, 'Please enter a valid email address.'),
    mobileNo: z
      .string()
      .optional()
      .refine((value) => {
        if (value) {
          return /^\+\d{8,20}$/.test(value);
        }
        return true;
      }, 'Please enter a valid mobile number.'),
    isInfiniteStock: z.boolean().optional(),
    proxyOpUnit: z.string().optional(),
    isActive: z.boolean(),
  })
  .refine((data) => !(data.isInfiniteStock && !data.proxyOpUnit), {
    message: 'Proxy Operational Unit is required when Infinite Stock is enabled.',
    path: ['proxyOpUnit'], // This points to the field that should show the error
  });

type formValues = z.infer<typeof validationSchema>;

const OpunitCreatePage: React.FC = () => {
  useDocumentTitle('Operational Units Create');
  const navigate = useNavigate();

  const [searchOpunitTerm, setSearchOpunitTerm] = React.useState('');

  const opunitOptions: { label: string; value: string }[] = [];
  const {
    data: opunitList,
    isLoading: isOpunitLoading,
    refetch: refetchOpunit,
  } = useGetOpunitList({
    page: 1,
    keyword: searchOpunitTerm,
    limit: 15,
  });
  if (!isOpunitLoading) {
    opunitOptions.push(
      ...(opunitList?.data?.items ?? []).map((opunit: any) => ({
        label: opunit.name,
        value: String(opunit.id),
      })),
    );
  }
  const handleOpunitSearch = (value: string) => {
    setSearchOpunitTerm(value);
    refetchOpunit();
  };

  const defaultValues = useMemo(
    () => ({
      name: '',
      address: '',
      emailId: '',
      mobileNo: '',
      isInfiniteStock: false,
      proxyOpUnit: '',
      isActive: true,
    }),
    [],
  );

  const form = useForm<formValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
  });
  const { setError } = form;
  const { mutateAsync: createOpunit, isPending: loading } = useCreateOpunit();

  const onSubmit = (data: formValues) => {
    console.log(data);
    createOpunit(data, {
      onSuccess: (data) => {
        console.log(data);
        navigate('/opunits');
      },
      onError: (error: any) => {
        console.log(error);
        if (error.response.status === 422) {
          const errors = error.response.data.errors;
          Object.keys(errors).forEach((key) => {
            setError(key as keyof formValues, {
              type: 'custom',
              message: errors[key][0],
            });
          });
        }
      },
    });
  };

  return (
    <MainLayout breadcrumbItems={breadcrumbItems}>
      <section className="page px-2 py-2">
        <div className="top-bar flex justify-between items-center px-4 py-2 bg-white rounded-md shadow-md">
          <div className="left-side text-xl font-semibold text-gray-700">Create User</div>
          <div className="right-side">
            <div className="flex items-center gap-2">
              <GoBackButton href="/opunits" label="Go Back" />
            </div>
          </div>
        </div>
        <div className="content mt-2">
          <Card className="rounded-md shadow-md">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextInput
                        control={form.control}
                        name="name"
                        label="Name"
                        placeholder="Enter your name"
                        isRequired
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <EmailInput control={form.control} name="emailId" label="Email" placeholder="Enter your email" />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextInput
                        control={form.control}
                        name="mobileNo"
                        label="Mobile Number"
                        placeholder="Enter your mobile number"
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <YesNoInput
                        control={form.control}
                        name="isActive"
                        label="Active Unit?"
                        placeholder="Operational Unit"
                        isRequired
                      />
                    </div>
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <YesNoInput
                        control={form.control}
                        name="isInfiniteStock"
                        label="Allow Infinite Stock"
                        placeholder="Infinite Stock"
                      />
                    </div>
                    {!isOpunitLoading && (
                      <div className="flex flex-col space-y-1 min-h-[80px]">
                        <SearchableSelectInput
                          control={form.control}
                          name="proxyOpUnit"
                          label="Proxy Operational Unit"
                          placeholder="Select a Operational Unit"
                          options={opunitOptions}
                          onSearch={handleOpunitSearch}
                          isLoading={isOpunitLoading}
                        />
                      </div>
                    )}
                    <div className="flex flex-col space-y-1 min-h-[80px]">
                      <TextAreaInput
                        control={form.control}
                        name="address"
                        label="Address"
                        placeholder="Enter your address"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end p-4">
                  <SubmitButton label="Create Operational Unit" isLoading={loading} />
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </section>
    </MainLayout>
  );
};

export default OpunitCreatePage;
